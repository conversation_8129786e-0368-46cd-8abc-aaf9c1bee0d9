<template>
	<view class="appointment-item">
		<view class="item-header flex col-center" :style="{ backgroundColor: info.dining_status === 'stop' ? '#e9e9e9' : '#fff' }">
			<view class="md f-w-500" v-if="type == 'all'">总订单号：{{ info.trade_no }}</view>
			<template v-else>
				<view class="flex-1 flex col-center lg f-w-500">
					<u-checkbox v-if="!isMealReportDetail && !info.order_report_meal_pack_id" :activeColor="variables.colorPrimary" :name="info.trade_no" :key="info.trade_no"></u-checkbox>
					<text>{{ info.meal_type_alias }}</text>
					<text v-if="orderType === 'reservation'" class="line-2 w-300 m-l-40">{{ info.stall_name }}</text>
					<view v-if="orderType === 'report_meal'" class="line-2 w-300 m-l-20 consume-type" :class="info.consume_type === 'online' ? '' : 'yellow-bg'">
						{{ info.consume_type === 'online' ? '线上支付' : '线下核销' }}
					</view>
				</view>
				<view class="warning">{{ info.take_meal_status_alias }}</view>
			</template>
		</view>
		<view class="item-goods p-t-30" v-if="type == 'now' && mode == 'appoint' && !isMealReportDetail">
			<view class="flex row-between m-b-30" v-for="(item, index) in info.food_list" :key="index">
				<view class="muted line-1">
					{{ item.name }}
					<text class="xs inline m-l-30">{{ item.spec }}{{ item.spec && item.taste ? '、' : '' }}{{ item.taste }}</text>
				</view>
				<view class="lg">x{{ item.count }}</view>
			</view>
		</view>
		<view class="item-info">
			<view class="flex row-between m-b-30">
				<view class="muted">支付状态：</view>
				<view>{{ info.order_status_alias }}</view>
			</view>
			<view class="flex row-between m-b-30">
				<view class="muted">点餐时间：</view>
				<!-- <view>{{timeFormat(time, 'yyyy mm dd hh:MM')}}</view> -->
				<view>{{ info.create_time }}</view>
			</view>
			<view class="flex row-between m-b-30">
				<view class="muted">就餐人：</view>
				<view>{{ info.user_name }}</view>
			</view>
			<view class="flex row-between m-b-30">
				<view class="muted">取餐方式：</view>
				<view>{{ info.take_meal_type_alias }}</view>
			</view>
			<template v-if="type == 'now'">
				<view class="flex row-between m-b-30">
					<view class="muted">份数：</view>
					<view>{{ info.count }}份</view>
				</view>
				<view class="flex row-between">
					<view class="muted">订单号：</view>
					<view>{{ info.trade_no }}</view>
				</view>
			</template>
			<template v-else>
				<view class="flex row-between m-b-30">
					<view class="muted">已预约餐段数：</view>
					<view>{{ info.count || info.children_count }}</view>
				</view>
				<view class="flex row-between">
					<view class="muted">使用优惠：</view>
					<view>已使用</view>
				</view>
			</template>
		</view>
		<view class="item-footer flex col-center">
			<view class="flex-1">
				<text class="muted m-r-8">合计</text>
				<price-format :price="type == 'now' ? info.pay_fee : info.total_fee" :size="36"></price-format>
				<!-- <text class="xxl m-l-8">￥{{info.pay_fee/100 || info.total_fee/100 }}</text> -->
			</view>
			<view class="flex" v-if="!isMealReportDetail">
				<!-- 非餐包订单是取消订单，餐包订单是停餐和恢复就餐 -->
				<view v-if="!info.order_report_meal_pack_id">
					<!-- 普通订单的取消 -->
					<u-button
						type="primary"
						v-if="!info.set_can_review && (info.take_meal_status !== 'cancel' && info.take_meal_status !== 'take_out' && info.take_meal_status !== 'time_out')"
						size="small"
						:plain="true"
						text="取消订单"
						@click="cancelOrder"
						:customStyle="customBtnStyle"
						:color="variables.colorPrimary"
					></u-button>
					<!-- 审核订单的取消 -->
					<u-button
						type="primary"
						v-if="info.set_can_review && info.cancel_review"
						size="small"
						:plain="true"
						text="取消订单"
						@click="cancelOrder"
						:customStyle="customBtnStyle"
						:color="variables.colorPrimary"
					></u-button>
				</view>
				<view v-if="info.order_report_meal_pack_id">
					<!-- 餐包的申请停餐/恢复就餐 -->
					<u-button
						type="primary"
						v-if="info.dining_status==='dining' && (info.take_meal_status !== 'cancel' && info.take_meal_status !== 'take_out' && info.take_meal_status !== 'time_out')"
						size="small"
						:plain="true"
						text="申请停餐"
						@click="stopDiningPackOrder(info, 'stop')"
						:customStyle="customBtnStyle"
						:color="variables.colorPrimary"
					></u-button>
					<u-button
						type="primary"
						v-if="info.dining_status==='stop' && (info.take_meal_status !== 'cancel' && info.take_meal_status !== 'take_out' && info.take_meal_status !== 'time_out')"
						size="small"
						:plain="true"
						text="恢复就餐"
						@click="stopDiningPackOrder(info, 'dining')"
						:customStyle="customBtnStyle"
						:color="variables.colorPrimary"
					></u-button>
				</view>
				<view v-if="orderType === 'report_meal' && type == 'now' && info.meal_number_display && info.dining_status==='dining'" class="m-l-20" @click="toCode(info)">
					<u-button type="primary" size="small" text="取餐码" :customStyle="customBtnStyle" :color="variables.colorPrimary"></u-button>
				</view>
				<view v-if="orderType === 'reservation' && type == 'now' && info.meal_number_display" class="m-l-20" @click="toCode(info)">
					<u-button type="primary" size="small" text="取餐码" :customStyle="customBtnStyle" :color="variables.colorPrimary"></u-button>
				</view>
				<view
					class="m-l-20"
					v-else-if="type == 'all' && info.take_meal_status !== 'cancel' && info.take_meal_status !== 'take_out'"
				>
					<router-link :to="`/pages_bundle/meal_public/appoint_detail?id=${info.trade_no}&order_type=${orderType}`">
						<u-button type="primary" size="small" text="查看详情" :customStyle="customBtnStyle" :color="variables.colorPrimary"></u-button>
					</router-link>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
import { timeFormat } from '@/utils/date'
import { getNonDuplicateID, encodeQuery } from '@/utils/util'
import { getApiMealPackOrderDiningStatus, getApiReportMealPackList } from '@/api/report_meal.js'
import Cache from '@/utils/cache'
export default {
	props: {
		type: String,
		orderType: {
			type: String,
			default: 'reservation'
		},
		mode: String,
		time: Number,
		info: Object,
		isMealReportDetail: {
			type: Boolean,
			default: false
		},
	},
	data() {
		return {
			timeFormat: timeFormat,
			getNonDuplicateID,
			customBtnStyle: {
				minWidth: '120rpx',
				height: '60rpx',
				lineHeight: '60rpx'
			},
			packOrderSettingInfo: {}
		}
	},

	methods: {
		cancelOrder() {
			// console.log(this.info)
			let orderTypeText = ''
			if (this.orderType === 'reservation') {
				orderTypeText = '预约点餐'
			} else if (this.orderType === 'report_meal') {
				orderTypeText = '报餐'
			}
			let _this = this
			let modalTitle = `确定取消${this.info.report_date} ${_this.type == 'now' ? this.info.meal_type_alias : ''}的${orderTypeText}吗？`
			if (this.info.is_subsidy_fee_order) {
				modalTitle = '如对应补贴已清零，退款后，相应金额也将被清零，是否继续？'
			}
			uni.showModal({
				title: '取消订单',
				content: modalTitle,
				/*  #ifndef MP-ALIPAY */
				// cancelColor: this.$vars.color_primary,
				// confirmColor: this.'#11E69E',
				/*  #endif  */
				confirmText: '取消订单',
				cancelText: '我再想想',
				success: function(res) {
					if (res.confirm) {
						if (_this.info.set_can_review && _this.info.cancel_review) {
							_this.$miRouter.push({
								path: '/pages_order/review/apply/index',
								query: {
									type: _this.orderType,
									trade_nos: encodeQuery([_this.info.trade_no]),
									date: encodeQuery([_this.info.report_date]), // 时间
									meal_types: encodeQuery([_this.info.meal_type]) // 餐段
								}
							})
							return
						}
						let tradeNo = _this.type == 'now' ? [_this.info.trade_no] : _this.info.trade_no
						// _this.confirmRefund(tradeNo, true)
						_this.$emit('refundconfirm', tradeNo)
					} else if (res.cancel) {
						// console.log('用户点击取消')
					}
				}
			})
		},

		toCode(data) {
			// let code
			// if (data.take_meal_type === 'cupboard') {
			// 	code = String(data.meal_number)
			// } else {
			// 	code = data.trade_no
			// }
			this.$miRouter.push({
				path: '/pages_bundle/meal_public/meal_code',
				query: {
					data: this.$encodeQuery({
						trade_no: data.trade_no,
						take_meal_type: data.take_meal_type,
						meal_number_qrcode: data.meal_number_qrcode,
						meal_number: data.meal_number
					})
				}
			})
		},
		// 恢复就餐 申请停餐
		stopDiningPackOrder(data, type) {
			if (type === 'stop') {
				this.getDiningPackOrderSetting(data, type)
			} else if (type === 'dining') {
				let modalText = `是否确认恢复 ${timeFormat(this.time)}${data.meal_type_alias}`
				this.stopDiningPackModal('恢复就餐', modalText, data, type)
			}
		},
		async getDiningPackOrderSetting(data, type) {
			this.$showLoading({
				title: '加载中...',
				mask: true
			})
			getApiReportMealPackList({
				person_no: Cache.get('userInfo').person_no,
        company_id: Cache.get('userInfo').company_id,
				order_report_meal_pack_id: data.order_report_meal_pack_id
      })
				.then(res => {
					uni.hideLoading()
					if (res.code == 0) {
						if (res.data && res.data.results.length) {
							this.packOrderSettingInfo = res.data.results[0]
						}
						let modalText
						if (this.packOrderSettingInfo.refund_type === 'manual') {
							modalText = `是否申请取消${timeFormat(this.time)}${data.meal_type_alias}？餐费将在工作人员审批后退回。如有疑问，请联系现场工作人员`
						} else if (this.packOrderSettingInfo.refund_type === 'meal_end') {
							modalText = `是否申请取消${timeFormat(this.time)}${data.meal_type_alias}？餐费将在${this.packOrderSettingInfo.manual_day}天后自动退回，如有疑问，请联系现场工作人员`
						} else if (this.packOrderSettingInfo.refund_type === 'specify_date') {
							if (this.packOrderSettingInfo.specify_date_json.specify_type === 'week') {
								modalText = `是否申请取消${timeFormat(this.time)}${data.meal_type_alias}？餐费将在每`
								this.packOrderSettingInfo.specify_date_json.week.map((item, index) => {
									modalText += `周${item}`
									if (index < this.packOrderSettingInfo.specify_date_json.week.length - 1) {
										modalText += '、'
									}
								})
								modalText += `统一退回`
							} else if (this.packOrderSettingInfo.specify_date_json.specify_type === 'month') {
								modalText = `是否申请取消${timeFormat(this.time)}${data.meal_type_alias}？餐费将在每月`
								this.packOrderSettingInfo.specify_date_json.days.map((item, index) => {
									if( item !== -1 ) {
										modalText += `${item}日`
									} else {
										modalText += `最后一日`
									}
									if (index < this.packOrderSettingInfo.specify_date_json.days.length - 1) {
										modalText += '、'
									}
								})
								modalText += `统一退回`
							} else if (this.packOrderSettingInfo.specify_date_json.specify_type === 'fixed') {
								modalText = `是否申请取消${timeFormat(this.time)}${data.meal_type_alias}？餐费将在`
								this.packOrderSettingInfo.specify_date_json.days.map((item, index) => {
									modalText += `${item}`
									if (index < this.packOrderSettingInfo.specify_date_json.days.length - 1) {
										modalText += '、'
									}
								})
								modalText += `统一退回`
							}
						}
						this.stopDiningPackModal('申请停餐', modalText, data, type)
					} else {
						uni.$u.toast(res.msg)
					}
				})
				.catch(error => {
					uni.$u.toast(error.message)
				})
		},
		async stopDiningPackModal(modalTitle, modalText, data, type) {
			let _this = this
			uni.showModal({
				title: modalTitle,
				content: modalText,
				confirmText: '确认',
				cancelText: '取消',
				success: function(res) {
					if (res.confirm) {
						_this.changePackOrderStatus(data, type)
					} else if (res.cancel) {
						// console.log('用户点击取消')
					}
				}
			})
		},
		async changePackOrderStatus(data, type) {
			this.$showLoading({
				title: '加载中...',
				mask: true
			})
			getApiMealPackOrderDiningStatus({
				trade_no: this.info.trade_no,
				dining_status: type,
				person_no: Cache.get('userInfo').person_no
			})
				.then(res => {
					uni.hideLoading()
					if (res.code == 0) {
						this.info.dining_status = type
					} else {
						uni.$u.toast(res.msg)
					}
				})
				.catch(err => {
					uni.hideLoading()
					uni.$u.toast(err.message)
				})
		}
	}
}
</script>

<style lang="scss" scoped>
.appointment-item {
	flex: 1;
	border-radius: 20rpx;
	background-color: #fff;
	position: relative;
	margin-bottom: 30rpx;
	overflow: hidden;

	// &::before,
	// &::after {
	// 	content: '';
	// 	display: block;
	// 	height: 30rpx;
	// 	width: 30rpx;
	// 	background-color: $background-color;
	// 	position: absolute;
	// 	border-radius: 50%;
	// 	top: 90rpx;
	// 	transform: translateY(-50%);
	// }

	// &::before {
	// 	left: -15rpx;
	// }

	// &::after {
	// 	right: -15rpx;
	// }

	.item-header {
		border-bottom: 1px dashed $border-color-base;
		height: 90rpx;
		padding: 0 30rpx;
		.consume-type{
			background-color: $color-primary;
			font-size: 24rpx;
			color: #fff;
			line-height: 42rpx;
			border-radius: 8rpx;
			padding: 0 10rpx;
			width: 120rpx;
			text-align: center;
		}
		.yellow-bg{
			background-color: #FD953C;
		}
	}

	.item-goods {
		border-bottom: $border-base;
		padding: 30rpx 30rpx 0 30rpx;
	}

	.item-info {
		border-bottom: $border-base;
		padding: 30rpx;
	}

	.item-footer {
		height: 100rpx;
		padding: 0 30rpx;
	}
	.w-300{
		width: 300rpx;
	}
}
</style>
