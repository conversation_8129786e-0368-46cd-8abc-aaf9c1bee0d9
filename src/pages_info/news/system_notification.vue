<template>
  <view :style="theme.style">
    <view class="system-notification flex flex-col col-center">
      <view class="system-notification-item p-t-40 p-b-20" v-for="(item, index) in systemNotificationList" :key="index">
        <view class="system-notification-item-time flex flex-center">
          <text>{{ formatTime(item.create_time) }}</text>
        </view>
        <view :class="['system-notification-item-content', item.extra && item.extra.reminder_type === 'expires_soon' ? 'isVIP' : 'noVIP']">
          <view class="system-notification-item-content-title flex col-center">
            <text>{{ item.title }}</text>
            <view v-if="item.extra && item.extra.reminder_type === 'expires_soon'" class="tip m-l-10">即将到期</view>
          </view>
          <view class="system-notification-item-content-box flex flex-col col-center">
            <view class="system-notification-item-content-box-text m-t-20 m-b-30">
              <text>{{ item.content }}</text>
            </view>
            <view class="system-notification-item-content-box-button">
              <u-button 
                :customStyle="buttonStyle"
                shape="circle"
                :color="item.extra && item.extra.reminder_type === 'expires_soon' ? 'linear-gradient(90deg, #FFD8A4, #FFB85C)' : 'linear-gradient(90deg, #8EACE7, #698DD3)'"
                @click="goToVIPPage(item)">
                <span :style="item.extra && item.extra.reminder_type === 'expires_soon' ? {color: '#460E0D'} : {color: '#fff'}">立即续费</span>
              </u-button>
            </view>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
import { getUserMessageList } from '@/api/app'
import dayjs from '../../uni_modules/uview-ui/libs/util/dayjs'
export default {
  data() {
    return {
      systemNotificationList: [],
      buttonStyle: {
        width: '356rpx',
        height: '68rpx'
      }
    }
  },
  onShow(){
    this.getMessageList()
  },
  computed: {
    formatTime() {
      return d => {
        return dayjs(d).format('YYYY年MM月DD日 HH:mm:ss')
      }
    }
  },
  methods: { 
    async getMessageList() {
      this.$showLoading({
        title: '获取通知中...',
        mask: true
      })
      let params = {
        page: 1,
        page_size: 10,
        pop_type: 'system_notice'
      }
      const [err, res] = await this.$to(getUserMessageList(params))
      uni.hideLoading()
      if (err) {
        return
      }
      if (res.code === 0) {
        console.log(res.data.results)
        this.systemNotificationList = res.data.results
      } else {
        uni.$u.toast(res.msg)
      }
    },
    goToVIPPage(item) {
      if (item.extra && (item.extra.reminder_type === 'vphone_expires_soon'|| item.extra.reminder_type === 'vphone_expired')) {
      // 购买套餐
      this.$miRouter.push({
          path: '/pages_third/voip/package_purchase'
        })
      } else {
        this.$miRouter.push({
          path: '/pages_member/member_center/VIP_page'
        })
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.system-notification {
  &-item {
    &-time {
      color: #8F9295;
    }
    &-content {
      width: 670rpx;
      height: 330rpx;
      position: relative;
      background-size: 100% 100%;
      background-repeat: no-repeat;
      &-title {
        position: absolute;
        top: 50rpx;
        left: 20rpx;
        font-size: 32rpx;
        .tip {
          width: 94rpx;
          height: 32rpx;
          border-radius: 8rpx;
          background-color: #ffefe1;
          color: #753822;
          font-size: 20rpx;
          text-align: center;
          line-height: 32rpx;
        }
      }
      &-box {
        width: 630rpx;
        height: 208rpx;
        position: absolute;
        bottom: 20rpx;
        left: 50%;
        transform: translate(-50%);
        &-text {
          padding: 0rpx 20rpx;
          font-size: 24rpx;
        }
        &-button {
          width: 356rpx;
          height: 68rpx;
        }
      }
      
    }
    .isVIP {
      background-image: url($imgBasePath + '/member/newVIP/member_message.png');
      color: #753822;
    }
    .noVIP {
      background-image: url($imgBasePath + '/member/newVIP/no_member_message.png');
      color: #485A82;
    }
  }
}
</style>