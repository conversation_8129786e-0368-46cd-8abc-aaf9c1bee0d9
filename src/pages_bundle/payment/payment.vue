<template>
	<view :style="theme.style" class="payment">
		<!-- Header 头部 Start -->
		<view class="header bg-primary lg white flex col-center row-between">
			<view class="">取餐方式</view>
			<view class="lg">
				<text v-if="takeMealType == 'on_scene'">堂食</text>
				<text v-else-if="takeMealType == 'waimai'">外卖</text>
				<text v-else-if="takeMealType == 'bale'">堂食自提</text>
				<text v-else-if="takeMealType == 'cupboard'">取餐柜</text>
			</view>
		</view>
		<!-- Header End -->

		<!-- Section 主体 Start -->
		<view class="section">
			<!-- Components PayWayCard Start -->
			<pay-way-card ref='payWayCard' :money="orderInfo.online_total_fee" @select="selectPayWay" :showPay="showPay" @showPayClose="showPayClose" :paymentOrderType="paymentOrderType" :orderInfo="orderInfo"></pay-way-card>
			<!-- Components PayWayCard End -->
			<view class="list m-t-20 ls-card bg-white md" @click="clickSelectCoupon" v-if="payWay.payway === 'PushiPay' && payWay.wallet_id && paymentOrderType === 'reservation' && Object.keys(reservationOrderGetMoneyInfo).length && !reservationOrderGetMoneyInfo.is_ok && showCoupon">
				<view class="offline flex row-between">
					<view class="md muted">
						<text>{{ selectCoupon.coupon_type_alias ? selectCoupon.coupon_type_alias : '选择优惠券' }}</text>
						<view class="tips xxs">手续费、打包费金额不参与优惠</view>
					</view>
					<view class="flex flex-center text-right">
						<view class="coupon-number" v-if="!selectCoupon.coupon_type_alias">{{ orderCouponCount }}张可用</view>
						<u-icon name="arrow-right" size="26rpx"></u-icon>
					</view>
					<!-- <price-format :price="orderInfo.offline_total_fee" :size="36"></price-format> -->
				</view>
			</view>

			<view class="list m-t-20 ls-card bg-white md">
				<view class="item flex row-between">
					<view class="md muted">手机号码：</view>
					<view class="flex flex-center text-right">
						<input v-model="phone" type="text" class="m-r-10" />
						<!-- <u-icon name="arrow-right" size="26rpx"></u-icon> -->
					</view>
				</view>
			</view>


      <view v-if="takeMealType == 'waimai'" class="">
				<view class="list m-t-20 ls-card bg-white md" v-if="isAddressVisitor">
					<view class="item bb flex row-between">
						<view class="md muted">就餐人：</view>
						<view class="flex flex-center text-right">
							<input v-model="payerName" type="text" class="m-r-10" />
						</view>
					</view>
				</view>

        <view class="list m-t-20 ls-card bg-white md">
        	<view class="item bb flex row-between">
        		<view style="width: 180rpx;" class="md muted">配送地址：</view>
        		<view class="flex row-right" @click="openAddressPopup">
        			<view class="md black">{{ addressInfo.full_addr_name }}</view>
        			<u-icon color="#999" name="arrow-right" size="24rpx"></u-icon>
        		</view>
        	</view>
        </view>

        <!-- <view v-if="takeMealType == 'waimai'" class="list m-t-20 ls-card bg-white md">
				<view class="item flex row-between">
					<view class="md muted">详细地址：</view>
					<view class="flex flex-center text-right">
						<input v-model="detailAddress" type="text" class="m-r-10" />
					</view>
				</view>
			</view> -->
      </view>

			<view class="list m-t-20 ls-card bg-white md">
				<view class="item bb flex row-between">
					<view class="md muted">订单原金额</view>
					<price-format :price="orderInfo.total_fee" :size="36"></price-format>
				</view>
				<view class="item bb flex row-between">
					<view class="md muted">优惠金额</view>
					<price-format :price="orderInfo.prefer_fee" :size="36"></price-format>
				</view>
				<view class="item bb flex row-between" v-if="!isAddressVisitor">
					<view class="md muted">扣款手续费</view>
					<view class="recharge-rate-fee">
						<price-format :price="commissionChargeFee.total_rate" :size="36"></price-format>
						<view class="icon-circle">
							<u-icon  name="error-circle" :color="variables.colorPrimary" size="36" @click="rechargeRateFeeDetails"></u-icon>
						</view>
					</view>
				</view>
				<view class="item bb flex row-between">
					<view class="md muted">线上支付金额</view>
					<price-format :price="orderInfo.online_total_fee" :size="36"></price-format>
				</view>
				<view class="offline flex row-between">
					<view class="md muted">
						<text>线下应付金额</text>
						<view class="tips xxs">1.服务费不参与优惠金额的计算</view>
						<view class="tips xxs">2.线下支付的订单不参与线上优惠活动</view>
					</view>
					<price-format :price="orderInfo.offline_total_fee" :size="36"></price-format>
				</view>
			</view>
			<view class="list m-t-20 ls-card bg-white md" v-if="!isAddressVisitor">
				<view class="item bb flex row-between">
					<view class="md muted">就餐人：</view>
					<view class="md black">{{ orderInfo.payer_name }}</view>
				</view>
			</view>

			<view class="list m-t-20 ls-card bg-white md">
				<view class="item flex row-between">
					<view class="md muted">备注：</view>
					<view class="flex flex-center text-right">
						<input v-model="remark" type="text" class="m-r-10" placeholder="例: 不要香菜, 谢谢" />
						<!-- <u-icon name="arrow-right" size="26rpx"></u-icon> -->
					</view>
				</view>
			</view>
			<view class="list m-b-20 m-t-20 ls-card bg-white md">
				<view class="item flex row-between">
					<view class="md muted">支付时间：</view>
					<view class="flex flex-center text-right">
						<!-- 15 * 60 * 1000 -->
						<u-count-down :time="timecount" format="DD:HH:mm:ss" autoStart millisecond @change="onTimeChange">
							<view class="time md">
								<text class="time__item p-r-5">剩余</text>
								<text class="time__item">{{ timeData.minutes }}&nbsp;分</text>
								<text class="time__item">{{ timeData.seconds }}&nbsp;秒</text>
								<text class="time__item">&nbsp;自动关闭</text>
							</view>
						</u-count-down>
						<!-- <u-icon name="arrow-right" size="26rpx"></u-icon> -->
					</view>
				</view>
			</view>
		</view>
		<!-- Section End -->

		<!-- Footer 底部 Start -->
		<view class="footer bg-white">
			<view class="footer--warpper flex row-between">
				<view class="muted flex col-center">
					<text class="m-r-20 md">需支付</text>
					<view class="black"><price-format :weight="500" :price="totalFee" :size="36"></price-format></view>
				</view>

				<view :class="['submit-btn', 'white', 'flex', 'flex-center',isLoading?'submit-btn-inactive':'']" @click="handlePrepay">支付</view>
			</view>
		</view>
		<!-- Footer End -->
		<u-modal :show="showTimeEnd" title="提示" @confirm="timeEndConfirm">
			<view class="" slots="default">支付时间已结束,请重新提交订单</view>
		</u-modal>
		<address-select
			v-if="showAddressPopup"
		  :show-address-popup.sync="showAddressPopup"
			:addr-id="addressInfo.addr_center_id"
			:org="paramsData.org.org_id"
			:rsv-orgs="orderInfo.stall_ids"
			@confirm="confirmAddress"
		>
		</address-select>
		<!-- 手续费 -->
		<u-modal
			:show="rechargeRateFeeShow"
			confirmText="关闭"
			:confirmColor="variables.colorPrimary"
			@confirm="rechargeRateFeeShow = false"
		>
			<view slot="default">
				<!-- <view>手续费详情</view> -->
				<view class="table-box">
					<uni-table ref="table" border  emptyText="暂无更多数据"  style="overflow: auto;">
						<!-- 表头行 -->
						<uni-tr>
							<uni-th width="65" align="center">组织</uni-th>
							<uni-th width="70" align="center">餐段</uni-th>
							<uni-th width="80" align="left">订单金额</uni-th>
							<uni-th width="65" align="left">手续费</uni-th>
						</uni-tr>
						<!-- 表格数据行 -->
						<uni-tr v-for="(item,index) in commissionChargeFee.result" :key="index">
							<uni-td>{{ nameFormat(item.organization,5) }}</uni-td>
							<uni-td>{{ item.meal_time }}</uni-td>
							<uni-td><price-format :price="item.origin_fee" :size="28"></price-format></uni-td>
							<uni-td><price-format :price="item.rate_fee" :size="28"></price-format></uni-td>
						</uni-tr>
					</uni-table>
				</view>
			</view>
		</u-modal>
		<!-- 警告框-->
		<u-modal :show="isShowDiffDialog" title="提示" content='亲，你手机的当前时间与标准时间存在差异，将会影响你下单支付，请尽快到系统进行设置！' :showCancelButton="false" @confirm="isShowDiffDialog = false"></u-modal>
	</view>
</template>

<script>
import { apiReservationGetOrderPay, apiSetUserAddress, apiReservationOrderCombinePay } from '@/api/user'
import { getApiWechatCongfigGet, getQywechatConfigGet,getDatetime } from '@/api/app'
import { apiOrderCouponList, apiMoneyByCoupon } from '@/api/coupon.js'
import { mapMutations, mapActions, mapGetters } from 'vuex'
import { reportMealOrderPay, getApiReportOrderGetMoney } from '@/api/report_meal.js'
import Cache from '@/utils/cache'
// #ifdef H5
var jweixin = null
// #endif
import { payMpRequest, checkWxBridgeReady, setWxJssdkConfig, payWxJssdkRequest } from '@/utils/payment'
import { checkClient, formateVisitorParams, getBaseUrl,plus, replaceDate } from '@/utils/util.js'
import AddressSelect from '../components/address-select/address-select.vue'
import UniTable from '../components/uni-table/components/uni-table/uni-table'
import UniTr from '../components/uni-table/components/uni-tr/uni-tr'
import UniTh from '../components/uni-table/components/uni-th/uni-th'
import UniTd from '../components/uni-table/components/uni-td/uni-td'
import { getApiGetAddrList,getApiCommissionChargeFee, getApiReservationOrderGetMoney,getApiReservationOrderGetMoneyAll } from '@/api/reservation.js'
import NP from '@/utils/np'
import { date } from '../../uni_modules/uv-ui-tools/libs/function/test'
export default {
	components: {
	  AddressSelect,
		UniTable,
		UniTr,
		UniTh,
		UniTd
	},
	// Data Start
	data() {
		return {
			paramsData: {},
			orderInfo: {
				prefer_fee: '0.00'
			},
			payWay: {},
			remark: '',
			takeMealType: '',
			paymentOrderType: '',
			isLoading: false,
			showPay: false, //打开支付方式
			timeData: {},
			showTimeEnd: false,
			platform: checkClient(), // 平台， 微信or支付宝
			tradeNo: '', // 记录下当前创建订单的订单号
			detailAddress: '',
			// 关于选择地址的
			showToSelectAddress: false, // 控制显示选择地址的
			addressList: [],
			showAddressPopup: false, // 显示地址选择器
			defaultAddressValue: [],
			rechargeRateFeeShow:false, // 手续费弹框
			commissionChargeFee:{total_rate: 0}, // 内容
			payerName: '',
      phone: '',
			orderCouponCount: 0,
			selectCoupon: {}, // 选择的优惠券
      timecount: 5 * 60 * 1000,
      isDiffTime: false, // 是否用户时间跟订单时间不一样，相差2分钟以上
      isShowDiffDialog: false,  // 警告弹窗
			reservationOrderGetMoneyInfo: {
				data: 0
			}, // 金额
      timer: null,
			waittingTime: 0,
			showCoupon: false, // 是否显示优惠券
		}
	},
	// Data End

	// Methods Start
	methods: {
		...mapMutations(['SET_SELECT']),
		...mapActions({
      setCodeInfo: 'setCodeInfo',
      setRemoveCouponItem: 'setRemoveCouponItem'
    }),
		onTimeChange(e) {
			this.timeData = e
		},
		timeEndConfirm() {
			this.$miRouter.back()
		},
    // 初始化支付
    async getWechatCongfigGet() {
      let params = {
        appid: uni.getStorageSync('appid') || Cache.get('userInfo').appid,
        company_id: Cache.get('userInfo').company_id,
        url: window.location.href.split('#')[0]
      }
			if (this.isAddressVisitor) {
				params.appid = uni.getStorageSync('appidVisitor')
			}
			let res = null
			try {
				if(this.platform === 'wechat'){
					res = await getApiWechatCongfigGet(formateVisitorParams(params))
				} else if(this.platform === 'wxwork'){
					res = await getQywechatConfigGet(params)
				}
				uni.hideLoading()
				if (res.code == 0) {
					if (res.data) {
						jweixin.config({
							beta: true,// 必须这么写，否则wx.invoke调用形式的jsapi会有问题
							debug: false,
							appId: res.data.appid,
							timestamp: res.data.timestamp,
							nonceStr: res.data.noncestr,
							signature: res.data.signature,
							jsApiList: ['checkJsApi', 'chooseWXPay','getBrandWCPayRequest'] // 把支付也初始化
						})
						jweixin.error(function(res){
							console.log('error~'+res)
						});
					}
				} else {
					uni.$u.toast(res.msg)
				}
			} catch (error) {
				uni.hideLoading()
				uni.$u.toast(error.message)
			}
    },
		handlePrepay() {
			// 外卖点餐
			if (this.paramsData.take_meal_type === 'waimai' && !this.addressInfo.addr_center_id) return uni.$u.toast('请选择配送地址')
      if(this.orderInfo.consume_type_all_offline && this.orderInfo.online_total_fee===0){
        this.$miRouter.replace({
          path: '/pages_bundle/payment/payment_result',
          query: {
            statusType: 'success',
            name:this.orderInfo.payer_name,
            pay_total_fee:this.orderInfo.total_fee,
            online_fee:this.orderInfo.online_total_fee,
						offline_fee:this.orderInfo.offline_total_fee,
            trade_no:this.orderInfo.unified_trade_no,
            pay_time:this.orderInfo.pay_time,
          }
        })
        return
      }
			this.tradeNo = '' // 清空上一次的记录
			if (this.isLoading) return uni.$u.toast('请稍等，并不要多次点击！')
			// 显示支付结束时间 没什么用 据说来骗骗产品和用户
			if (!this.isDiffTime && !this.timeData.minutes && !this.timeData.seconds) return (this.showTimeEnd = true)

			if (!this.phone) return uni.$u.toast('请填写手机号')
			if (!this.payerName && this.isAddressVisitor && this.paramsData.take_meal_type === 'waimai') return uni.$u.toast('请填写就餐人')

			// 如果只有一条数据 默认打开弹框 产品要求
			if (!this.payWay.payinfo_id || (!this.payWay.wallet_id && this.payWay.payway == 'PushiPay')) return (this.showPay = true)
			let _this = this
			if (!this.payWay.payinfo_id) return uni.$u.toast('请选择支付方式')
			if (!this.payWay.wallet_id && this.payWay.payway == 'PushiPay') return uni.$u.toast('请选择钱包')
			this.timer = setInterval(() => {
				this.waittingTime ++
			}, 1000)
			this.$showLoading({
				title: '支付中...',
				mask: true
			})
			this.isLoading = true
			let api
			let params = {
				balance_type: this.payWay.balance_type,
				payinfo_id: this.payWay.payinfo_id,
				balance_type: this.payWay.balance_type,
				balance_type: this.payWay.payway === 'PushiPay' ? this.payWay.balance_type : '',
				unified_trade_no: this.orderInfo.unified_trade_no,
				remark: this.remark,
				take_meal_type: this.paramsData.take_meal_type,
				payment_order_type: this.paramsData.payment_order_type,
				company_id: Cache.get('userInfo').company_id,
				user_id: Cache.get('userInfo').user_id,
				org_id: this.paramsData.org.org_id,
				// #ifdef H5
				return_url: getBaseUrl() + 'pages_bundle/payment/payment_result' // 支付完成跳转的地址针对location.href这种方式的充值
				// #endif
			}
			if (this.payWay.payway === 'PushiPay') {
				params.wallet_id = this.payWay.wallet_id
			}
			let originBackUrl = Cache.get('originBackUrl')
      if (originBackUrl) {
        params.backUrl = originBackUrl
      }
			if (this.paymentOrderType === 'reservation') {
				api = apiReservationGetOrderPay
				if (this.payWay.balance_type === 'online_combine_wallet') {
				api = apiReservationOrderCombinePay
				}
				apiReservationOrderCombinePay
				if (this.paramsData.take_meal_type === 'waimai') { // 外卖点餐
					params.addr_center_id = this.addressInfo.addr_center_id
					params.address = this.addressInfo.full_addr_name
					params.detailed_addr = this.detailAddress
					params.username = this.isAddressVisitor ? this.payerName : this.orderInfo.payer_name
					params.phone = this.phone
				}
				params.coupon_id = this.selectCoupon.id
			} else if (this.paymentOrderType === 'report_meal') {
				params.order_pack_id = this.orderInfo.order_pack_id
				api = reportMealOrderPay
			}
			api(formateVisitorParams(params, true))
				.then(res => {
					if (res.code == 0) {
						this.clearIntervalFun() // 清除餐包的定时器
						this.SET_SELECT({
							key: 'cupboard',
							data: {}
						})
						this.tradeNo = res.data.trade_no
						// 游客要保留上一次的就餐人和手机号
						if (this.isAddressVisitor) {
							uni.setStorageSync('visitorName', this.payerName)
							uni.setStorageSync('visitorPhone', this.phone)
						}
						// 外卖要保存用户地址
						if (this.paramsData.take_meal_type === 'waimai' && !this.isAddressVisitor) {
							this.setAddress(this.addressInfo.addr_center_id, this.detailAddress)
						}

						// 成功的订单，已签约免密支付的会走这一步
						if (res.data.pay_result && res.data.pay_result.order_status === 'ORDER_SUCCESS') {
							// uni.hideLoading()
							uni.showToast({
								title: '支付成功',
								icon: 'success',
								success: () => {
									this.gotoPayResult()
								}
							})
							return
						}
						// 支付成功
						if (res.data.order_status === 'ORDER_SUCCESS') {
							uni.showToast({
								title: '支付成功',
								icon: 'success',
								success: () => {
									this.gotoPayResult()
								}
							})
							return
						}
						// #ifdef H5
						if (res.data.pay_result.extra && res.data.pay_result.extra.redirect) {
              window.location.href = res.data.pay_result.extra.redirect
              return
            }
						// #endif

						if (
							res.data.pay_result.payway === 'WechatPay' ||
							res.data.pay_result.payway === 'QyWechatPay' ||
							(res.data.pay_result.payway == 'AliPay' && res.data.pay_result.sub_payway === 'miniapp')
						) {
							this.jsapiChooseWXPay(res.data.pay_result.extra)
						} else if ((res.data.pay_result.payway === 'AliPay' || res.data.pay_result.payway === 'WXYFPay') && res.data.pay_result.sub_payway === 'h5') {
							// Cache.set('PAYORDERTRADENO', this.tradeNo)
							uni.hideLoading()
							// this.isLoading = false
							window.location.href = res.data.pay_result.extra
						} else {
							uni.hideLoading()
							// this.isLoading = false
							// uni.showToast({
							// 	title: '支付成功',
							// 	icon: 'success',
							// 	// duration: 2000,
							// 	success: () => {
							// 		this.gotoPayResult()
							// 	}
							// })
						}
					} else if (res.code === 1) {
						this.clearIntervalFun() // 清除餐包的定时器
						this.$miRouter.replace({
							path: '/pages_bundle/payment/payment_result',
							query: {
								statusType: 'error',
								statusMsg: res.msg
							}
						})
					} else {
						this.clearIntervalFun() // 清除餐包的定时器
						uni.hideLoading()
						this.isLoading = false
						uni.$u.toast(res.msg)
					}
					// this.$miRouter.replace('/pages_bundle/payment/payment_result')
				})
				.catch(err => {
					this.clearIntervalFun() // 清除餐包的定时器
					this.isLoading = false
					uni.$u.toast(err.message)
					uni.hideLoading()
				})
		},
		async selectPayWay(value) {
			console.log("selectPayWay", value);
			this.payWay = value
			await this.getCommissionChargeFee()
			// 只有预约订单需要
			// if (this.paymentOrderType === 'reservation') {
			if(this.payWay.payway === 'PushiPay' && !this.payWay.wallet_id){
				// 如果没有选钱包的时候别调用
				return
			}
			this.showPay = false
			// 单开一个计算的接口 只有组合支付
			if (this.payWay.balance_type === 'online_combine_wallet') {
				this.getOrderMoneyAll()
			}else {
				this.getOrderMoney()
			}
			// }
		},
		showPayClose(value) {
			this.showPay = value
		},
		jsapiChooseWXPay(params) {
			let _this = this
			let paymentOrderType = this.$store.state.appoint.select.payment_order_type
			// #ifdef H5
				// 企业微信 和微信支付共用
				checkWxBridgeReady(params, function({res}) {
					if (res.err_msg == 'get_brand_wcpay_request:ok') {
						// 使用以上方式判断前端返回,微信团队郑重提示：
						//res.err_msg将在用户支付成功后返回ok，但并不保证它绝对可靠。
						uni.showToast({
							title: '支付成功',
							icon: 'success',
							// duration: 2000,
							success: () => {
								// _this.$miRouter.back(2)
								uni.hideLoading()
								_this.isLoading = false
								_this.gotoPayResult()
							}
						})
					} else {
						uni.showToast({
							title: '支付失败',
							icon: 'none',
							success: () => {
								_this.isLoading = false
								uni.hideLoading()
							}
						})
					}
				})
			// #endif
			// #ifdef MP-WEIXIN || MP-ALIPAY
			payMpRequest(params)
				.then(({ res, provider }) => {
					uni.hideLoading()
					if (provider === 'alipay') {
						// 当为支付宝支付时需要额外判断状态码
						switch (res.resultCode) {
							case '9000': // 订单处理成功。
								uni.showToast({
									title: '支付成功',
									icon: 'success',
									success: () => {
										// _this.$miRouter.back(2)
										_this.isLoading = false
										_this.gotoPayResult()
									}
								})
								break
							case '6001': // 用户中途取消
								uni.showToast({
									title: '用户中途取消',
									icon: 'fail'
								})
								_this.isLoading = false
								break
							case '8000': // 正在处理中。支付结果未知（有可能已经支付成功）。
								uni.showToast({
									title: '正在处理中',
									icon: 'success',
									success: () => {
										// _this.$miRouter.back(2)
										_this.isLoading = false
										_this.gotoPayResult()
									}
								})
								break
							case '6002': // 网络连接出错
								_this.isLoading = false
								uni.showToast({
									title: '网络连接出错',
									icon: 'fail'
								})
								break
							case '6004': // 处理结果未知（有可能已经成功）
								uni.showToast({
									title: '正在处理中',
									icon: 'success',
									success: () => {
										// _this.$miRouter.back(2)
										_this.isLoading = false
										_this.gotoPayResult()
									}
								})
								break
							case '4': // 无权限调用
								_this.isLoading = false
								uni.showToast({
									title: '无权限调用',
									icon: 'fail'
								})
								break
							default:
								uni.showToast({
									title: '支付失败',
									icon: 'none'
								})
								break
						}
					} else {
						uni.hideLoading()
						uni.showToast({
							title: '支付成功',
							icon: 'success',
							success: () => {
								// _this.$miRouter.back(2)
								_this.isLoading = false
								_this.gotoPayResult()
							}
						})
					}
				})
				.catch((res, provider) => {
					_this.isLoading = false
					uni.showToast({
						title: '支付失败',
						icon: 'none'
					})
				})
			// #endif
		},
		async gotoPayResult(tradeNo, sleep) {
			// Cache.remove('PAYORDERTRADENO')
			if (sleep) {
				await this.$sleep(1000)
			}
			this.isLoading = false
			uni.hideLoading()
      uni.removeStorageSync('codeappid') // 扫码的话，清空码的appid缓存
			this.setCodeInfo(null) // 扫码的话，清空码的缓存
			this.$miRouter.replace({
				path: '/pages_bundle/payment/payment_result',
				query: {
					// #ifdef MP-ALIPAY
					// data: this.$encodeQuery(res.data)
					// #endif
					// #ifndef MP-ALIPAY
					// data: res.data
					// #endif
					trade_no: tradeNo ? tradeNo : this.tradeNo
				}
			})
			// await this.$sleep(1500)
			// this.$miRouter.replace({path:'/pages_bundle/appoint/user_appoint'})
		},
		setAddress(addr_center_id, address_detail) {
			apiSetUserAddress({
				addr_center_id,
				address_detail,
        org_id: this.paramsData.org.org_id
			})
        .then(res => {
          uni.hideLoading()
          if (res.code == 0) {
          } else {
            uni.$u.toast(res.msg)
          }
        })
        .catch(err => {
          uni.$u.toast(err.message)
        })
		},
		// 关于地址的
		openAddressPopup() {
			if (this.addressInfo.type === 'nocode' || this.addressInfo.can_modify_addr) {
				this.showAddressPopup = true
			} else {
				return uni.$u.toast('不支持扫码更改地址')
			}
		},
		confirmAddress(e) {
			this.defaultAddressValue = e.value
			this.showAddressPopup = false;
			if (this.addressInfo.type === 'nocode') { // 非扫码
				this.SET_SELECT({
					key: 'address_info',
					data: {
						type: 'nocode',
						addr_center_id: this.defaultAddressValue[this.defaultAddressValue.length - 1],
						full_addr_name: e.full_addr_name
					}
				})
			} else {  // 扫码支持修改地址的
				this.SET_SELECT({
				  key: 'address_info',
				  data: {
						type: 'code', // type用于区分扫码或者非扫码
						addr_center_id: this.defaultAddressValue[this.defaultAddressValue.length - 1],
						full_addr_name: e.full_addr_name,
						can_modify_addr: this.addressInfo.can_modify_addr,
            company_id: this.addressInfo.company_id
					}
				})
			}
		},
		rechargeRateFeeDetails(){
			this.rechargeRateFeeShow = true
		},
		// 获取钱包手续费
		getCommissionChargeFee() {
			return new Promise((resolve) => {
				if (this.isAddressVisitor) {
					resolve(false)
				}
				let params = {
					order_id: this.orderInfo.unified_trade_no,
					company_id: Cache.get('userInfo').company_id,
					organization_id: this.paramsData.org.org_id,
					payinfo_id: this.payWay.payinfo_id,
					user_id: Cache.get('userInfo').user_id,
				}
				if (this.orderInfo.payer_person_no) {
					params.person_no = this.orderInfo.payer_person_no
				}
				if (this.payWay.wallet_id) {
					params.wallet_id = this.payWay.wallet_id
				}
				this.$showLoading({
					title: '获取中....',
					mask: true
				})
				getApiCommissionChargeFee(params)
					.then(res => {
						uni.hideLoading()
						if (res.code == 0) {
							this.commissionChargeFee = res.data
						} else {
							uni.$u.toast(res.msg)
						}
						resolve(true)
					})
					.catch(err => {
						uni.hideLoading()
						// console.log('获取付款码err', err)
						uni.$u.toast(err.data.msg)
						resolve(false)
					})
			})
		},
		// 格式化文字 超过多少显示...
		nameFormat(name,number) {
			if(!name) return
			let subStr = name.slice(0, number)
			subStr = subStr + (name.length > number ? '...' : '')
			return subStr
		},
		// 预约订单结算金额计算
    getOrderMoney() {
			// if(!this.payWay.wallet_id) return
			let params = {
				unified_trade_no: this.orderInfo.unified_trade_no,
				balance_type: this.payWay.payway === 'PushiPay' ? this.payWay.balance_type : '',
				payinfo_id: this.payWay.payinfo_id,
				balance_type: this.payWay.balance_type,
				remark: this.remark,
				take_meal_type: this.paramsData.take_meal_type,
				payment_order_type: this.paramsData.payment_order_type,
				company_id: Cache.get('userInfo').company_id,
				user_id: Cache.get('userInfo').user_id,
				org_id: this.paramsData.org.org_id,
				calculate_chupay: true
			}
			if (this.payWay.payway === 'PushiPay') {
				params.wallet_id = this.payWay.wallet_id
			}
			// if (this.paymentOrderType === 'reservation') {
				// params.calculate_chupay = true
			// }
			uni.showLoading({
				title: '金额核算中，请稍等...',
				mask: true
			})
			this.isLoading = true
			let api
			if (this.paymentOrderType === 'reservation') {
				api = getApiReservationOrderGetMoney
			} else if (this.paymentOrderType === 'report_meal') {
				api = getApiReportOrderGetMoney
			}
      api(params)
        .then(res => {
          uni.hideLoading()
					this.isLoading = false
          if (res.code == 0) {
						// 是否显示优惠券
						this.showCoupon = true
            if (this.paymentOrderType === 'report_meal') {
              this.reservationOrderGetMoneyInfo.data = res.data
            }else {
              // is_ok 是否有优惠规则 后台设置有优惠规则不显示优惠券选择
              this.reservationOrderGetMoneyInfo = res.data
            }
						// let price = res.data
						// if(typeof price === 'object'){
						// 	price = price.data || 0
						// }
            this.$set(this.orderInfo, 'online_total_fee', res.data.data)
						// 计算优惠金额 订单原金额-线上应付金额-线下应付金额-手续费
						var totalRate = this.commissionChargeFee.total_rate || 0
						var perferFee = NP.minus(NP.plus(this.orderInfo.online_total_fee, this.orderInfo.offline_total_fee), this.orderInfo.total_fee, totalRate)
						console.log("perferFee", perferFee);
						this.$set(this.orderInfo, 'prefer_fee', perferFee || 0)
						console.log("this.refs.payWayCard ",this.$refs.payWayCard );
						if(this.$refs.payWayCard && Reflect.has(this.$refs.payWayCard,'compareMoney')){
              this.$refs.payWayCard.compareMoney(res.data)
						}
            if (this.payWay.payway === 'PushiPay' && this.payWay.wallet_id  && this.paymentOrderType === 'reservation' && !this.reservationOrderGetMoneyInfo.is_ok) {
              this.getApiOrderCouponList()
              if (Object.keys(this.selectCouponItem).length) {
                this.selectCoupon = this.selectCouponItem
                // 预约订单使用优惠券结算查看价格
                this.getApiMoneyByCoupon()
              }
            }
          } else {
            uni.$u.toast(res.msg)
          }
        })
        .catch(err => {
          uni.hideLoading()
					this.isLoading = false
          // console.log('获取付款码err', err)
          // uni.$u.toast(err.data.msg)
        })
    },
		// 组合支付 预约订单结算金额计算
		getOrderMoneyAll() {
			// if(!this.payWay.wallet_id) return
			let params = {
				unified_trade_no: this.orderInfo.unified_trade_no,
				balance_type: this.payWay.payway === 'PushiPay' ? this.payWay.balance_type : '',
				payinfo_id: this.payWay.payinfo_id,
				balance_type: this.payWay.balance_type,
				remark: this.remark,
				take_meal_type: this.paramsData.take_meal_type,
				payment_order_type: this.paramsData.payment_order_type,
				company_id: Cache.get('userInfo').company_id,
				user_id: Cache.get('userInfo').user_id,
				org_id: this.paramsData.org.org_id,
			}
			if (this.payWay.payway === 'PushiPay') {
				params.wallet_id = this.payWay.wallet_id
			}
			uni.showLoading({
				title: '金额核算中，请稍等...',
				mask: true
			})
			this.isLoading = true
			let api = getApiReservationOrderGetMoneyAll
			// if (this.paymentOrderType === 'reservation' && this.payWay.balance_type === 'online_combine_wallet'){
			// 	api = getApiReservationOrderGetMoneyAll
			// } else if (this.paymentOrderType === 'reservation') {
			// 	api = getApiReservationOrderGetMoney
			// } else if (this.paymentOrderType === 'report_meal') {
			// 	api = getApiReportOrderGetMoney
			// }
      api(params)
        .then(res => {
          uni.hideLoading()
					this.isLoading = false
          if (res.code == 0) {
						// 优惠券
            if (this.paymentOrderType === 'report_meal') {
              // this.reservationOrderGetMoneyInfo.data = res.data
            }else {
              // is_ok 是否有优惠规则 后台设置有优惠规则不显示优惠券选择
              this.reservationOrderGetMoneyInfo = {
								data:res.data.combine_pay_fee,
								is_ok:res.data.is_ok,
							}
            }
						let price = res.data
						console.log(price, 99999, typeof price)
						if(typeof price === 'object'){
							price = price.combine_pay_fee || 0
						}
						// online_total_fee 线上支付金额
            this.$set(this.orderInfo, 'online_total_fee', price)
						// 计算优惠金额 订单原金额-线上应付金额-线下应付金额-手续费
						var totalRate = this.commissionChargeFee.total_rate || 0
						var perferFee = NP.minus(NP.plus(this.orderInfo.online_total_fee, this.orderInfo.offline_total_fee), this.orderInfo.total_fee, totalRate)
						// console.log("perferFee", perferFee);
						this.$set(this.orderInfo, 'prefer_fee', perferFee || 0)
						// console.log("this.refs.payWayCard ",this.$refs.payWayCard );
						if(this.$refs.payWayCard && Reflect.has(this.$refs.payWayCard,'compareMoney')){
              this.$refs.payWayCard.compareMoney(res.data)
						}
            if (this.payWay.payway === 'PushiPay' && this.payWay.wallet_id  && this.paymentOrderType === 'reservation' && !this.reservationOrderGetMoneyInfo.is_ok) {
              this.getApiOrderCouponList()
              if (Object.keys(this.selectCouponItem).length) {
                this.selectCoupon = this.selectCouponItem
                // 预约订单使用优惠券结算查看价格
                this.getApiMoneyByCoupon()
              }
            }
          } else {
            uni.$u.toast(res.msg)
          }
        })
        .catch(err => {
          uni.hideLoading()
					this.isLoading = false
          // console.log('获取付款码err', err)
          // uni.$u.toast(err.data.msg)
        })
    },
		// 对比时间
		checkDate(time) {
			let nowTime = new Date().getTime()
			let payTime = new Date(time).getTime()
			if (Math.abs(payTime - nowTime) > 5 * 60 * 1000) {
				this.isShowDiffDialog = true
				this.isDiffTime = true
			}
		},
		// 计算时间
    computedTime() {
			uni.showLoading()
      getDatetime()
        .then(res => {
          uni.hideLoading()
          if (res.code == 0) {
						if (this.orderInfo?.pay_time) {
							let paytime = replaceDate(this.orderInfo.pay_time)
							let nowtime = replaceDate(res.data.now)
							this.timecount = new Date(paytime).getTime() + 5 * 60 * 1000 - new Date(nowtime).getTime()
							this.checkDate(paytime)
						}
          } else {
            uni.$u.toast(res.msg)
          }
        })
        .catch(err => {
          uni.hideLoading()
          // console.log('获取付款码err', err)
          uni.$u.toast(err.data.msg)
        })
    },
    clickSelectCoupon(){
      this.$miRouter.push({
        path: '/pages_bundle/payment/select_coupon',
        query: {
          unifiedTradeNo: this.orderInfo.unified_trade_no,
        }
      })
    },
    // 加载优惠券接口 显示多少张可用
    async getApiOrderCouponList() {
      this.$showLoading({
        title: '加载中....',
        mask: true
      })
      const [err, res] = await this.$to(
          apiOrderCouponList({
            company_id: Cache.get('userInfo').company_id,
            user_id: Cache.get('userInfo').user_id,
            unified_trade_no: this.orderInfo.unified_trade_no
          })
      )
      uni.hideLoading()
      if (err) {
        // 请求失败,隐藏加载状态
        uni.$u.toast(err.message)
        return
      }
      if (res.code === 0) {
        this.orderCouponCount = res.data.results.filter(obj => obj.is_use === true).length
      }
    },
		// 预约订单使用优惠券结算查看价格
		async getApiMoneyByCoupon() {
      this.$showLoading({
        title: '加载中....',
        mask: true
      })
			let params = {
				unified_trade_no: this.orderInfo.unified_trade_no,
				balance_type: this.payWay.payway === 'PushiPay' ? this.payWay.balance_type : '',
				payinfo_id: this.payWay.payinfo_id,
				balance_type: this.payWay.balance_type,
				remark: this.remark,
				take_meal_type: this.paramsData.take_meal_type,
				payment_order_type: this.paramsData.payment_order_type,
				company_id: Cache.get('userInfo').company_id,
				user_id: Cache.get('userInfo').user_id,
				org_id: this.paramsData.org.org_id,
				coupon_id: this.selectCoupon.id,
			}
			if (this.payWay.payway === 'PushiPay') {
				params.wallet_id = this.payWay.wallet_id
			}
      const [err, res] = await this.$to(
				apiMoneyByCoupon(params)
      )
      uni.hideLoading()
      if (err) {
        // 请求失败,隐藏加载状态
        uni.$u.toast(err.message)
        return
      }
      if (res.code === 0) {
				let moneyByCoupon = res.data
				// 重新拿 这个是使用过的优惠券金额 塞进去
				this.$set(this.orderInfo, 'online_total_fee', moneyByCoupon.online_fee || 0)
				this.$set(this.orderInfo, 'prefer_fee', moneyByCoupon.total_deduction_fee || 0)
      } else {
				console.log(999)
				// 清除优惠券
				this.selectCoupon = {} // 清空优惠券数据
        this.setRemoveCouponItem()
        uni.$u.toast(res.msg)
      }
    },
		// 清除倒计时
    clearIntervalFun() {
      if (this.timer) {
        clearInterval(this.timer)
        this.timer = null
      }
    },
	},
	// Methods End

	// Life Cycle Start
	watch: {
		waittingTime() {
			console.log(this.waittingTime, (this.waittingTime-5)%3, !(this.waittingTime-5)%3)
			if (this.waittingTime === 5) {
				uni.hideLoading()
				this.$showLoading({
					title: '订餐数量较多，正在为您支付中  请勿离开页面，耐心等候...',
					mask: true
				})
			} else if (!((this.waittingTime-5)%3)) {
				if (!((this.waittingTime-5)%6)) {
					uni.hideLoading()
					this.$showLoading({
						title: '订餐数量较多，正在为您支付中  请勿离开页面，耐心等候...',
						mask: true
					})
				}
				uni.hideLoading()
				this.$showLoading({
					title: '请勿进行操作，以免支付失败',
					mask: true
				})
			}
		}
	},
	computed: {
		...mapGetters(['isAddressVisitor', 'selectCouponItem']),
		addressInfo() { // vuex保存的地址信息
			if (this.$store.state.appoint.select.address_info) { // 保存的地址信息
				return this.$store.state.appoint.select.address_info
			} else {
				return {}
			}
		},
		// 总需要支付的金额
		totalFee() {
			let fee = this.orderInfo.online_total_fee
				// if(this.orderInfo && this.orderInfo.total_fee ){  如果是报餐就要加上手续费，如果是点餐就是用线上金额就行
					// if(this.paymentOrderType === 'report_meal' && this.commissionChargeFee && this.commissionChargeFee.total_rate){
					// 	fee = plus(this.orderInfo.online_total_fee,this.commissionChargeFee.total_rate)
					// }else
					//  {
					// 	fee = this.orderInfo.online_total_fee
					// }
				// }
			return fee
		},
	},
	onShow() {
		// is_ok 是否有优惠规则 后台设置有优惠规则不显示优惠券选择
		// this.reservationOrderGetMoneyInfo.is_ok 是否显示优惠券 true不显实
		// 只有储值钱包才有优惠券
		if (this.payWay.payway === 'PushiPay' && this.payWay.wallet_id && Object.keys(this.reservationOrderGetMoneyInfo).length && this.paymentOrderType === 'reservation') {
			if (!this.reservationOrderGetMoneyInfo.is_ok && Object.keys(this.selectCouponItem).length) {
				if (this.selectCoupon !== this.selectCouponItem) {
					this.selectCoupon = this.selectCouponItem
					// 预约订单使用优惠券结算查看价格
					this.getApiMoneyByCoupon()
				}

			}else {
				this.selectCoupon = {} // 清空优惠券数据
				this.$set(this.orderInfo, 'online_total_fee', this.reservationOrderGetMoneyInfo.data || 0)
				// 计算优惠金额 订单原金额-线上应付金额-线下应付金额-手续费
				var totalRate = this.commissionChargeFee.total_rate || 0
				var perferFee = NP.minus(NP.plus(this.orderInfo.online_total_fee, this.orderInfo.offline_total_fee), this.orderInfo.total_fee, totalRate)
				this.$set(this.orderInfo, 'prefer_fee', perferFee || 0)
			}
		}
	},
	onLoad() {
		// this.getPayInfo()
		this.paramsData = this.$store.state.appoint.select
		this.orderInfo = this.$Route.query.data
		this.orderInfo.prefer_fee = '0'
		this.takeMealType = this.$store.state.appoint.select.take_meal_type
		this.paymentOrderType = this.$store.state.appoint.select.payment_order_type
		console.log("	this.orderInfo", 	this.orderInfo,"this.takeMealType", this.takeMealType,"this.paymentOrderType ", this.paymentOrderType );
		// 计算时间
		this.computedTime()

		if (this.isAddressVisitor) { // 游客要显示上一次的就餐人和手机号
			this.phone = uni.getStorageSync('visitorPhone')
			this.payerName = uni.getStorageSync('visitorName')
		} else {
			this.phone = this.$store.getters.userInfo.phone
		}
		// #ifdef H5
		if (this.platform === 'wechat') {
			jweixin = require('jweixin-module')
		}else if(this.platform === 'wxwork'){
			//企业微信
			jweixin = require('weixin-jweixin').default
		}
    if (this.platform === 'wechat' || this.platform === 'wxwork') {
			// 因为企业微信需要初始化
      this.getWechatCongfigGet()
    }
		// 支付宝h5支付成功后跳回来会带上out_trade_no的
		// let tradeNo = Cache.get('PAYORDERTRADENO')
		if (this.$Route.query.out_trade_no) {
			this.$showLoading({
				title: '支付中....',
				mask: true
			})
			this.gotoPayResult(this.$Route.query.out_trade_no, true)
			return
		}
		// #endif
	}
	// Life Cycle End
}
</script>

<style lang="scss">
.payment {
	.ls-card {
		border-radius: 20rpx;
	}

	.header {
		height: 90rpx;
		padding: 0 40rpx;
	}

	.section {
		padding: 40rpx;
		padding-bottom: calc(100rpx + env(safe-area-inset-bottom));

		.bb {
			border-bottom: 1px solid $border-color-base;
		}

		.list {
			padding: 0 30rpx;

			.item {
				padding: 32rpx 0;
				box-sizing: border-box;
			}

			.offline {
				padding: 20rpx 0;

				.tips {
					color: #f8a63c;
				}
				.coupon-number{
					// width: 110rpx;
					// height: 44rpx;
					padding: 8rpx 15rpx;
					color: #fff;
					font-size: 24rpx;
					background-color: #fe5858;
					border-radius: 8rpx;
				}
			}
		}
	}
	.recharge-rate-fee{
		position: relative;
		.icon-circle{
			position: absolute;
			top: -28rpx;
			right: -20rpx;
		}
	}
	.footer {
		width: 100%;
		position: fixed;
		bottom: 0;
		left: 0;
		padding-left: 38rpx;
		border-top: 1px solid #eaecee;
		padding-bottom: env(safe-area-inset-bottom);

		&--warpper {
			height: 100rpx;
		}

		.submit-btn {
			width: 200rpx;
			height: 100%;
			background-color: $color-primary;
		}
		.submit-btn-inactive {
			background-color: #B1B1B1;
			color: #fff;
		}
	}
	.table-box {
		max-height: 600rpx;
		overflow: auto;
	}
}
</style>
