<template>
  <view :style="theme.style" class="consumption-limit">
    <!-- 滚动容器 -->
    <!-- <scroll-view class="scroll-container" scroll-y="true" :style="{ height: scrollHeight + 'rpx' }"> -->
    <!-- 提示信息 -->
    <view class="tip-info">
      <view class="muted">请注意, 此功能只支持线下店内支付,且不兼容设备开启离线支付。</view>
      <view class="muted">如设置单独消费点限额,优先限制消费点,再限制全局。</view>
    </view>
    <!-- 全局限制 -->
    <view class="setting-section bg-white">
      <view class="section-title">
        <view class="flex row-between col-center">
          <view class="black nr">全局限制</view>
          <u-switch
            :activeColor="variables.colorPrimary"
            v-model="globalLimit.enabled"
            @change="onGlobalLimitChange"
            :size="34"
          ></u-switch>
        </view>
      </view>

      <!-- 每日限制金额 -->
      <view class="setting-item" v-if="globalLimit.enabled">
        <view class="flex row-between col-center">
          <view class="black nr">每日限制金额（元）</view>
          <view class="input-wrapper">
            <u-number-box
              inputWidth="80px"
              @change="onGlobalAmountChange"
              @blur="onGlobalAmountBlur"
              integer
              :min="1"
              :step="1"
              v-model="globalLimit.dailyAmount"
            >
              <view slot="minus">
                <view class="minus">
                  <u-icon name="minus" color="inherit" size="20rpx"></u-icon>
                </view>
              </view>
              <view slot="plus" class="plus">
                <u-icon name="plus" color="inherit" size="20rpx"></u-icon>
              </view>
            </u-number-box>
          </view>
        </view>
      </view>
    </view>
    <view class="setting-section bg-white">
      <!-- 单独消费限制设置 -->
      <view class="setting-item">
        <view class="flex row-between col-center">
          <view class="black nr">单独消费限制设置</view>
          <u-switch
            :activeColor="variables.colorPrimary"
            v-model="individualLimit.enabled"
            @change="onIndividualLimitChange"
            :size="34"
          ></u-switch>
        </view>
      </view>
      <!-- 消费点设置列表 -->
      <view class="consumption-points" v-if="individualLimit.enabled">
        <view class="points-list">
          <view class="point-item" v-for="(point, index) in consumptionPoints" :key="index">
            <view class="point-header">
              <view class="flex row-between col-center">
                <view class="black nr">{{ point.name }}</view>
                <u-switch
                  :activeColor="variables.colorPrimary"
                  v-model="point.enabled"
                  @change="onPointLimitChange(index)"
                  :size="34"
                ></u-switch>
              </view>
            </view>

            <!-- 每日限制金额 -->
            <view class="point-amount" v-if="point.enabled">
              <view class="flex row-between col-center">
                <view class="black nr">每日限制金额（元）</view>
                <view class="input-wrapper">
                  <u-number-box
                    inputWidth="80px"
                    @change="onPointAmountChange(index)"
                    @blur="onPointAmountBlur(index)"
                    integer
                    :min="1"
                    :step="1"
                    v-model="point.dailyAmount"
                  >
                    <view slot="minus">
                      <view class="minus">
                        <u-icon name="minus" color="inherit" size="20rpx"></u-icon>
                      </view>
                    </view>
                    <view slot="plus" class="plus">
                      <u-icon name="plus" color="inherit" size="20rpx"></u-icon>
                    </view>
                  </u-number-box>
                </view>
              </view>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 底部占位，避免内容被固定按钮遮挡 -->
    <!-- <view class="bottom-placeholder"></view> -->
    <!-- </scroll-view> -->

    <!-- 保存按钮 -->
    <view class="footer bg-white">
      <view class="footer-wrapper flex flex-center">
        <u-button
          text="保存设置"
          shape="circle"
          :color="variables.bgLinearGradient1"
          @click="saveSettings"
          :loading="saving"
        ></u-button>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      saving: false,
      scrollHeight: 0, // 滚动容器高度
      // 全局限制设置
      globalLimit: {
        enabled: true,
        dailyAmount: 10 // 使用数字类型，默认最小值为1
      },
      // 单独消费限制设置
      individualLimit: {
        enabled: true
      },
      // 消费点列表
      consumptionPoints: [
        {
          name: '消费点名称1',
          enabled: true,
          dailyAmount: 200 // 使用数字类型
        },
        {
          name: '消费点名称2',
          enabled: false,
          dailyAmount: 200
        },
        {
          name: '消费点名称3',
          enabled: true,
          dailyAmount: 200
        },
        {
          name: '消费点名称4',
          enabled: true,
          dailyAmount: 200
        },
        {
          name: '消费点名称5',
          enabled: false,
          dailyAmount: 200
        },
        {
          name: '消费点名称6',
          enabled: true,
          dailyAmount: 200
        }
      ]
    }
  },

  methods: {
    // 计算滚动容器高度
    calculateScrollHeight() {
      const systemInfo = uni.getSystemInfoSync()
      const windowHeight = systemInfo.windowHeight
      const footerHeight = 120 // 底部按钮区域高度，单位px
      this.scrollHeight = windowHeight - footerHeight
    },

    // 全局限制开关变化
    onGlobalLimitChange(value) {
      this.globalLimit.enabled = value
      // if (!value) {
      // 关闭全局限制时，也关闭单独限制
      // this.individualLimit.enabled = false
      // }
    },

    // 单独消费限制开关变化
    onIndividualLimitChange(value) {
      this.individualLimit.enabled = value
    },

    // 消费点限制开关变化
    onPointLimitChange(index) {
      // 开关状态已经通过v-model自动更新
      console.log(`消费点${index + 1}限制状态:`, this.consumptionPoints[index].enabled)
    },

    // 全局金额变化处理
    onGlobalAmountChange(value) {
      console.log('全局金额变化:', value)
      this.globalLimit.dailyAmount = this.validateAmount(value)
    },

    // 全局金额失焦处理
    onGlobalAmountBlur(value) {
      console.log('全局金额失焦:', value)
      this.globalLimit.dailyAmount = this.validateAmount(value)
    },

    // 消费点金额变化处理
    onPointAmountChange(index, value) {
      console.log(`消费点${index + 1}金额变化:`, value)
      this.consumptionPoints[index].dailyAmount = this.validateAmount(value)
    },

    // 消费点金额失焦处理
    onPointAmountBlur(index, value) {
      console.log(`消费点${index + 1}金额失焦:`, value)
      this.consumptionPoints[index].dailyAmount = this.validateAmount(value)
    },

    // 验证金额，确保最小值为1
    validateAmount(value) {
      // 处理各种输入情况
      let numValue = Number(value)

      // 如果是NaN、0、负数或空值，则设为1
      if (isNaN(numValue) || numValue <= 0 || value === '' || value === null || value === undefined) {
        return 1
      }

      // 确保是整数
      return Math.floor(numValue)
    },

    // 加载当前设置
    async loadSettings() {
      try {
        // 这里添加获取当前设置的API调用
        // const response = await api.getConsumptionLimitSettings()
        // this.globalLimit = response.globalLimit
        // this.individualLimit = response.individualLimit
        // this.consumptionPoints = response.consumptionPoints
      } catch (error) {
        console.error('加载设置失败:', error)
      }
    },

    // 验证所有金额
    validateAllAmounts() {
      // 验证全局金额
      this.globalLimit.dailyAmount = this.validateAmount(this.globalLimit.dailyAmount)

      // 验证所有消费点金额
      this.consumptionPoints.forEach(point => {
        point.dailyAmount = this.validateAmount(point.dailyAmount)
      })
    },
    // 保存设置
    async saveSettings() {
      this.saving = true
      try {
        // 这里添加保存设置的API调用
        const settings = {
          globalLimit: this.globalLimit,
          individualLimit: this.individualLimit,
          consumptionPoints: this.consumptionPoints
        }

        console.log('保存设置:', settings)

        // 模拟API调用
        await new Promise(resolve => setTimeout(resolve, 1000))

        uni.showToast({
          title: '保存成功',
          icon: 'success'
        })

        // 可以选择保存后返回上一页
        // setTimeout(() => {
        //   uni.navigateBack()
        // }, 1500)
      } catch (error) {
        console.error('保存失败:', error)
        uni.showToast({
          title: '保存失败',
          icon: 'error'
        })
      } finally {
        this.saving = false
      }
    }
  },

  onLoad() {
    // 计算滚动容器高度
    this.calculateScrollHeight()
    // 页面加载时可以从API获取当前设置
    this.loadSettings()
    // 初始化时验证所有金额
    this.validateAllAmounts()
  },

  onReady() {
    // 页面渲染完成后重新计算高度，确保准确性
    this.$nextTick(() => {
      this.calculateScrollHeight()
    })
  }
}
</script>

<style lang="scss" scoped>
.consumption-limit {
  height: calc(100vh - 180rpx);
  overflow: auto;
  padding: 40rpx;
  background-color: $background-color;
  .bottom-placeholder {
    height: 40rpx; // 底部留白，避免内容被按钮遮挡
  }
  .tip-info {
    margin-bottom: 20rpx;

    text {
      font-size: 24rpx;
      line-height: 1.5;
    }
  }
  .setting-section {
    margin-bottom: 20rpx;
    border-radius: 20rpx;
    padding: 0 40rpx;

    .section-title {
      padding: 40rpx 0;
      border-bottom: 1rpx solid $border-color-base;
    }
    .setting-item {
      padding: 20rpx 0;
      &:not(:last-child) {
        border-bottom: 1rpx solid $border-color-base;
      }
    }
    .minus,
    .plus {
      color: $color-primary;
      width: 36rpx;
      height: 36rpx;
      display: flex;
      justify-content: center;
      align-items: center;
      border: 1px solid $color-primary;
      border-radius: 4rpx;
    }

    .plus {
      margin-left: 20rpx;
      background-color: $color-primary;
      color: #fff;
      border-color: $color-primary;
    }
    .minus {
      margin-right: 20rpx;
    }
  }
  .consumption-points {
    border-radius: 20rpx;
    margin-bottom: 20rpx;
    .points-list {
      .point-item {
        &:not(:last-child) {
          border-bottom: 1rpx solid $border-color-base;
        }
        .point-header {
          padding: 40rpx 0;
        }
        .point-amount {
          padding-bottom: 40rpx;
        }
      }
    }
  }

  .footer {
    width: 100%;
    position: fixed;
    bottom: 0;
    left: 0;
    padding: 0 40rpx;
    border-top: 1px solid #eaecee;
    padding-bottom: env(safe-area-inset-bottom);

    .footer-wrapper {
      padding-top: 20rpx;
      padding-bottom: 20rpx;
    }
  }
}
</style>
