 {
	"pages": [ //pages数组中第一项表示应用启动页，参考：https://uniapp.dcloud.io/collocation/pages
		{
			"path": "pages/index/index",
			"style": {
				"navigationBarTitleText": "首页",
				"navigationStyle": "custom"
			},
			"meta": {
				"auth": true
			}
		},
		{
			"path": "pages/health_center/health_center",
			"style": {
				"navigationBarTitleText": "健康中心",
				"navigationStyle": "custom"
			},
			"meta": {
				"auth": true
			}
		},
		{
			"path": "pages/discovery/index",
			"style": {
				"navigationBarTitleText": "发现"
			},
			"meta": {
				"auth": true
			}
		},
		{
			"path": "pages/photo_recognition/photo_recognition",
			"style": {
				"navigationBarTitleText": "菜品识别",
				"navigationStyle": "custom"
			},
			"meta": {
				"auth": false
			}
		},
		// {
		// 	"pagePath": "pages/photo_recognition/photo_recognition",
		// 	"iconPath": "static/icons/tab_photo.png",
		// 	"selectedIconPath": "static/icons/tab_photo_s.png",
		// 	"text": "饮食记录"
		// },
		{
			"path": "pages/photo_recognition/result",
			"style": {
				"navigationBarTitleText": "识别结果",
				"navigationStyle": "custom"
			},
			"meta": {
				"auth": false
			}
		},
		{
			"path": "pages/user/user",
			"style": {
				"navigationBarTitleText": "个人中心"
			}
		},
		{
			"path": "pages/login/login",
			"style": {
				"navigationBarTitleText": "朴食健康",
				"navigationStyle": "custom"
			}
		},
		{
			"path": "pages/login/wx_login",
			"style": {
				"navigationBarTitleText": "朴食健康"
			}
		},
		{
			"path": "pages/login/mp_login",
			"style": {
				"navigationBarTitleText": "朴食健康",
				"navigationStyle": "custom"
			}
		},
    {
			"path": "pages/nutrition_circle/nutrition_circle",
			"style": {
				"navigationBarTitleText": "营养圈",
				"onReachBottomDistance": 20
			},
			"meta": {
				"auth": true
			}
		},
		{
			"path": "pages/register/auto_register",
			"style": {
				"navigationBarTitleText": "自注册",
				"onReachBottomDistance": 20,
				"navigationStyle": "custom"
			},
			"meta": {
				"auth": true
			}
		}
	],
	"subPackages": [{
			"root": "pages_health",
			"pages": [
				// {
				// 	"path": "health_center/health_center",
				// 	"style": {
				// 		"navigationBarTitleText": "健康中心"
				// 	},
				// 	"meta": {
				// 		"auth": true
				// 	}
				// },
				{
					"path": "healthy/assessment/healthy_assessment_entrance",
					"style": {
						"navigationBarTitleText": "健康测评"
					},
					"meta": {
						"auth": true
					}
				},
				{
					"path": "healthy/assessment/healthy_assessment",
					"style": {
						"navigationBarTitleText": "健康测评内容"
					},
					"meta": {
						"auth": true
					}
				},
				{
					"path": "healthy/assessment/health_assessment_results",
					"style": {
						"navigationBarTitleText": "健康测评结果"
					},
					"meta": {
						"auth": true
					}
				},
				{
					"path": "healthy/body_management/examination_report",
					"style": {
						"navigationBarTitleText": "体检报告",
						"enablePullDownRefresh": true,
						"onReachBottomDistance": 150
					},
					"meta": {
						"auth": true
					}
				},
				{
					"path": "article/article_details",
					"style": {
						"navigationBarTitleText": "文章详情"
					},
					"meta": {
						"auth": true
					}
				},
				{
					"path": "article/article",
					"style": {
						"navigationBarTitleText": "文章"
					},
					"meta": {
						"auth": true
					}
				},
				{
					"path": "healthy/menu/menu_recommend",
					"style": {
						"navigationBarTitleText": "食谱推荐",
						"onReachBottomDistance": 20 // 距离底部多远时触发 单位为px
					},
					"meta": {
						"auth": true
					}
				},
				{
					"path": "healthy/sport/add_sport",
					"style": {
						"navigationBarTitleText": "添加运动"
					},
					"meta": {
						"auth": true
					}
				},
				{
					"path": "healthy/sport/add_custom_sport",
					"style": {
						"navigationBarTitleText": "添加自定义运动"
					},
					"meta": {
						"auth": true
					}
				},
				{
					"path": "healthy/diet_guide/diet_guide",
					"style": {
						"navigationBarTitleText": "膳食指南"
					},
					"meta": {
						"auth": true
					}
				},
				{
					"path": "healthy/diet_healthy/index",
					"style": {
						"navigationBarTitleText": "饮食健康"
					},
					"meta": {
						"auth": true
					}
				},
				{
					"path": "healthy/diet_healthy/foodCorrect",
					"style": {
						"navigationBarTitleText": "菜品纠正"
					},
					"meta": {
						"auth": true
					}
				},
				{
					"path": "healthy/diet_healthy/nutritionalAnalysis",
					"style": {
						"navigationBarTitleText": "营养分析"
					},
					"meta": {
						"auth": true
					}
				},
				{
					"path": "bind_healthy/bind_healthy",
					"style": {
						"navigationBarTitleText": "绑定健康数据"
					},
					"meta": {
						"auth": true
					}
				},
				{
					"path": "record_food/manual_record",
					"style": {
						"navigationBarTitleText": "记录饮食",
						"navigationStyle": "custom"
					},
					"meta": {
						"auth": true
					}
				},
				{
					"path": "record_food/search_manual_record",
					"style": {
						"navigationBarTitleText": "搜索食物"
					},
					"meta": {
						"auth": true
					}
				},
				{
					"path": "record_food/food_details",
					"style": {
						"navigationBarTitleText": "食物详情"
					},
					"meta": {
						"auth": true
					}
				},
				{
					"path": "record_food/food_custom",
					"style": {
						"navigationBarTitleText": "自定义食物"
					},
					"meta": {
						"auth": true
					}
				},
				{
					"path": "record_food/food_custom_list",
					"style": {
						"navigationBarTitleText": "自定义食物"
					},
					"meta": {
						"auth": true
					}
				},
				{
					"path": "healthy/habit_formation/index",
					"style": {
						"navigationBarTitleText": "习惯养成"
					},
					"meta": {
						"auth": true
					}
				},
				{
					"path": "healthy/habit_formation/add_custom_habit",
					"style": {
						"navigationBarTitleText": "添加自定义习惯"
					},
					"meta": {
						"auth": true
					}
				}
			]
		},
		{
			"root": "pages_info",
			"pages": [{
					"path": "userinfo/choice_items",
					"style": {
						"navigationBarTitleText": "选择项目点"
					},
					"meta": {
						"auth": true
					}
				},
				{
					"path": "userinfo/bind_items",
					"style": {
						"navigationBarTitleText": "绑定项目点"
					},
					"meta": {
						"auth": true
					}
				},
				{
					"path": "userinfo/perfect_userinfo",
					"style": {
						"navigationBarTitleText": "完善个人信息"
					},
					"meta": {
						"auth": true
					}
				},
				{
					"path": "userinfo/diet_taboos",
					"style": {
						"navigationBarTitleText": "选择禁忌食物"
					},
					"meta": {
						"auth": true
					}
				},
				{
					"path": "account_info/account_info",
					"style": {
						"navigationBarTitleText": "账户信息"
					},
					"meta": {
						"auth": true
					}
				},
				{
					"path": "account_info/info_edit",
					"style": {
						"navigationBarTitleText": "编辑"
					},
					"meta": {
						"auth": true
					}
				},
				{
					"path": "account_info/detail",
					"style": {
						"navigationBarTitleText": "用户信息"
					},
					"meta": {
						"auth": true
					}
				},
				{
					"path": "account_info/user_items",
					"style": {
						"navigationBarTitleText": "我的项目"
					},
					"meta": {
						"auth": true
					}
				},
				{
					"path": "account_info/change_pwd",
					"style": {
						"navigationBarTitleText": "修改密码"
					},
					"meta": {
						"auth": true
					}
				},
				{
					"path": "face_gather/face_gather",
					"style": {
						"navigationBarTitleText": "人脸采集"
					},
					"meta": {
						"auth": false
					}
				},
				{
					"path": "face_gather/face_gather_treaty",
					"style": {
						"navigationBarTitleText": "人脸采集协议"
					},
					"meta": {
						"auth": false
					}
				},
				{
					"path": "face_gather/face_photograph",
					"style": {
						"navigationBarTitleText": "人脸采集"
					},
					"meta": {
						"auth": false
					}
				},
				{
					"path": "face_gather/face_photograph_result",
					"style": {
						"navigationBarTitleText": "人脸采集"
					},
					"meta": {
						"auth": false
					}
				},
				{
					"path": "wallet/wallet",
					"style": {
						"navigationBarTitleText": "朴食科技智慧食堂"
					},
					"meta": {
						"auth": true
					}
				},
				{
					"path": "wallet/stored_value_wallet",
					"style": {
						"navigationBarTitleText": "储值钱包"
					},
					"meta": {
						"auth": true
					}
				},
				{
					"path": "wallet/recharge_wallet",
					"style": {
						"navigationBarTitleText": "充值提现"
					},
					"meta": {
						"auth": true
					}
				},
				{
					"path": "wallet/wallet_details",
					"style": {
						"navigationBarTitleText": "明细"
					},
					"meta": {
						"auth": true
					}
				},
				{
					"path": "wallet/statistical_analysis",
					"style": {
						"navigationBarTitleText": "统计分析"
					},
					"meta": {
						"auth": true
					}
				},
				{
					"path": "wallet/bill",
					"style": {
						"navigationBarTitleText": "账单"
					},
					"meta": {
						"auth": true
					}
				},
				{
					"path": "wallet/wallet_center",
					"style": {
						"navigationBarTitleText": "钱包中心"
					},
					"meta": {
						"auth": true
					}
				},
				{
					"path": "user_config/index",
					"style": {
						"navigationBarTitleText": "设置"
					},
					"meta": {
						"auth": true
					}
				},
				{
					"path": "user_config/free_payment_setting",
					"style": {
						"navigationBarTitleText": "免密设置"
					},
					"meta": {
						"auth": true
					}
				},
				{
					"path": "user_config/setting/free_payment_setting_user",
					"style": {
						"navigationBarTitleText": "设置"
					},
					"meta": {
						"auth": true
					}
				},
				{
					"path": "user_config/page_admin/index",
					"style": {
						"navigationBarTitleText": "页面管理"
					},
					"meta": {
						"auth": true
					}
				},
				{
					"path": "user_config/about_us/index",
					"style": {
						"navigationBarTitleText": "关于我们"
					},
					"meta": {
						"auth": true
					}
				},
				{
					"path": "user_config/cancellation_application/index",
					"style": {
						"navigationBarTitleText": "注销申请"
					},
					"meta": {
						"auth": true
					}
				},
				{
					"path": "user_config/cancellation_application/under_Application",
					"style": {
						"navigationBarTitleText": "注销申请"
					},
					"meta": {
						"auth": true
					}
				},
				{
					"path": "user_config/cancellation_application/inconsistent_conditions",
					"style": {
						"navigationBarTitleText": "注销账号"
					},
					"meta": {
						"auth": true
					}
				},
				{
					"path": "user_config/sign_admin/abc_sign_apply",
					"style": {
						"navigationBarTitleText": "签约申请",
						"navigationStyle": "custom"
					}
				},
				{
					"path": "user_config/sign_admin/abc_signing_success",
					"style": {
						"navigationBarTitleText": "签约申请",
						"navigationStyle": "custom"
					}
				},
				{
					"path": "agreement/clause",
					"style": {
						"navigationBarTitleText": "条款"
					},
					"meta": {
						"auth": true
					}
				},
				{
					"path": "agreement/policy",
					"style": {
						"navigationBarTitleText": "条款"
					},
					"meta": {
						"auth": true
					}
				},
				{
					"path": "news/user_news",
					"style": {
						"navigationBarTitleText": "我的消息"
					},
					"meta": {
						"auth": true
					}
				},
				{
					"path": "news/project_news",
					"style": {
						"navigationBarTitleText": "消息通知"
					},
					"meta": {
						"auth": true
					}
				},
				{
					"path": "news/questionnaire",
					"style": {
						"navigationBarTitleText": "问卷调查",
						"navigationStyle": "custom"
					},
					"meta": {
						"auth": true
					}
				},
				{
					"path": "news/system_notification",
					"style": {
						"navigationBarTitleText": "系统通知"
					},
					"meta": {
						"auth": true
					}
				},
				{
					"path": "news/news_details",
					"style": {
						"navigationBarTitleText": "消息通知"
					},
					"meta": {
						"auth": true
					}
				},
				{
					"path": "news/notification_detail",
					"style": {
						"navigationBarTitleText": "服务通知"
					},
					"meta": {
						"auth": true
					}
				},
				{
					"path": "news/weekly_report",
					"style": {
						"navigationBarTitleText": "饮食健康周报"
					},
					"meta": {
						"auth": true
					}
				},
				{
					"path": "news/sign_page",
					"style": {
						"navigationBarTitleText": "签名",
						"screenOrientation": "landscape"
					},
					"meta": {
						"auth": true
					}
				},
				{
					"path": "user_config/psbc_sign/sign_apply",
					"style": {
						"navigationBarTitleText": "签约申请",
						"navigationStyle": "custom"
					}
				},
				{
					"path": "user_config/psbc_sign/signing_success",
					"style": {
						"navigationBarTitleText": "签约申请",
						"navigationStyle": "custom"
					}
				},
				{
					"path": "agreement/list",
					"style": {
						"navigationBarTitleText": "协议"
					}
				},
				{
					"path": "agreement/detail",
					"style": {
						"navigationBarTitleText": "协议",
						"navigationStyle": "custom"
					}
				},
				{
        	"path": "user_config/abc_account/abc_account_apply",
        	"style": {
        		"navigationBarTitleText": "快捷支付",
        		"navigationStyle": "custom"
        	}
        },
        {
        	"path": "user_config/abc_account/abc_account_success",
        	"style": {
        		"navigationBarTitleText": "快捷支付",
        		"navigationStyle": "custom"
        	}
        },
        {
        	"path": "user_config/abc_account/abc_un_eaccount",
        	"style": {
        		"navigationBarTitleText": "电子账户"
        	}
        },
				{
        	"path": "user_config/ccb_account/ccb_account_apply",
        	"style": {
        		"navigationBarTitleText": "信息补充"
        	}
        },
				{
					"path": "evaluate/user_evaluate",
					"style": {
						"navigationBarTitleText": "我的评价"
					},
					"meta": {
						"auth": true
					}
				},
				{
					"path": "evaluate/evaluation_details",
					"style": {
						"navigationBarTitleText": "评价详情"
					},
					"meta": {
						"auth": true
					}
				},
				{
					"path": "feedback/history_log",
					"style": {
						"navigationBarTitleText": "历史记录"
					},
					"meta": {
						"auth": true
					}
				},
				{
					"path": "feedback/log_details",
					"style": {
						"navigationBarTitleText": "历史记录详情"
					},
					"meta": {
						"auth": true
					}
				},
				{
					"path": "feedback/proposal_complaint",
					"style": {
						"navigationBarTitleText": "建议反馈"
					},
					"meta": {
						"auth": true
					}
				},
				{
					"path": "feedback/system_advise",
					"style": {
						"navigationBarTitleText": "建议反馈"
					},
					"meta": {
						"auth": true
					}
				},
				{
					"path": "feedback/canteen_advise",
					"style": {
						"navigationBarTitleText": "食堂反馈"
					},
					"meta": {
						"auth": true
					}
				},
				{
					"path": "user_config/alipay_face/sign_apply",
					"style": {
						"navigationBarTitleText": "一脸通行",
						"navigationStyle": "custom"
					}
				},
				{
					"path": "user_config/meal_subsidy/sign_apply",
					"style": {
						"navigationBarTitleText": "餐补使用",
						"navigationStyle": "custom"
					}
				},
				{
					"path": "user_config/qy_code/sign_apply",
					"style": {
						"navigationBarTitleText": "企业码"
					},
					"meta": {
						"auth": true
					}
				},
				{
					"path": "user_config/user_information_collection/user_info_collection",
					"style": {
						"navigationBarTitleText": "用户信息收集",
						"navigationStyle": "custom"
					},
					"meta": {
						"auth": true
					}
				},
				{
					"path": "user_config/customer_service/customer_service",
					"style": {
						"navigationBarTitleText": "客服与帮助"
					},
					"meta": {
						"auth": true
					}
				},
				{
					"path": "user_config/theme_setting/index",
					"style": {
						"navigationBarTitleText": "切换主题"
					},
					"meta": {
						"auth": true
					}
				},
				{
					"path": "consumption_limit/consumption_limit",
					"style": {
						"navigationBarTitleText": "消费限制设置"
					},
					"meta": {
						"auth": true
					}
				}
			]
		},
		{
			"root": "pages_health_pomr",
			"pages": [
				{
					"path": "healthy_archives",
					"style": {
						"navigationBarTitleText": "健康档案",
						"navigationStyle": "custom"
					},
					"meta": {
						"auth": true
					}
				},
				{
					"path": "physical_testing",
					"style": {
						"navigationBarTitleText": "身体数据"
					},
					"meta": {
						"auth": true
					}
				},
        {
          "path": "reports_list/index",
          "style": {
						"navigationBarTitleText": "饮食列表"
					},
          "meta": {
            "auth": true
          }
        },
        {
          "path": "diet_report/index",
          "style": {
            "enablePullDownRefresh": false,
            "navigationStyle": "custom",
            "navigationBarTextStyle": "white",
            "mp-alipay": {
              "transparentTitle": "always",
              "titlePenetrate": "YES",
              "navigationBarTitleText": "饮食报告",
              "barButtonTheme": "light"
            }
          }
        },
				{
					"path": "weight_target",
					"style": {
						"navigationBarTitleText": "体重目标"
					},
					"meta": {
						"auth": true
					}
				},
				{
					"path": "diet_archives/index",
					"style": {
						"navigationBarTitleText": "饮食档案"
					},
					"meta": {
						"auth": true
					}
				},
				{
					"path": "diet_archives/food_category",
					"style": {
						"navigationBarTitleText": "食物种类"
					},
					"meta": {
						"auth": true
					}
				},
				{
					"path": "diet_archives/food_ranking",
					"style": {
						"navigationBarTitleText": "食物占比排行"
					},
					"meta": {
						"auth": true
					}
				},
				{
					"path": "motion_archives/index",
					"style": {
						"navigationBarTitleText": "运动档案"
					},
					"meta": {
						"auth": true
					}
				},
				{
					"path": "weight_calendar",
					"style": {
						"navigationBarTitleText": "体重日历"
					},
					"meta": {
						"auth": true
					}
				},
				{
					"path" : "examination_report/index",
					"style" :  {
						"navigationBarTitleText": "体检报告"
					},
				 	"meta": {
						"auth": true
					}
				},
				{
					"path" : "examination_report/details",
					"style" :  {
						"navigationBarTitleText": "体检报告详情",
						"navigationStyle": "custom"
          },
					"meta": {
						"auth": true
					}
        },
				{
					"path" : "menu_plan/detail",
					"style" :  {
						"navigationBarTitleText": "食谱详情"
          },
					"meta": {
						"auth": true
					}
        },
				{
					"path" : "menu_plan/message",
					"style" :  {
						"navigationBarTitleText": "写留言"
          },
					"meta": {
						"auth": true
					}
        },
				{
					"path" : "menu_plan/index",
					"style" :  {
						"navigationBarTitleText": "食谱计划"
          },
					"meta": {
						"auth": true
					}
        },
				{
					"path" : "nutritional_analysis/index",
					"style" :  {
						"navigationBarTitleText": "营养分析"
          },
					"meta": {
						"auth": true
					}
        },
				{
					"path": "nutritional_analysis/detail",
					"style": {
						"navigationStyle": "custom"
					},
					"meta": {
						"auth": true
					}
				},
				{
					"path": "fileInformation/fileInformation",
					"style": {
						"navigationBarTitleText": "档案信息"
					},
					"meta": {
						"auth": true
					}
				}
			]
		},
		{
			"root": "pages_bundle",
			"pages": [{
					"path": "appoint/user_appoint",
					"style": {
						"navigationBarTitleText": "我的预约",
						"navigationStyle": "custom",
						"disableScroll": true
					},
					"meta": {
						"auth": true
					}
				},
				{
					"path": "meal_public/appoint_detail",
					"style": {
						"navigationBarTitleText": "详情"
					},
					"meta": {
						"auth": true
					}
				},
				{
					"path": "appoint/appoint_order",
					"style": {
						"navigationBarTitleText": "预约点餐",
						"navigationStyle": "custom",
						"enablePullDownRefresh": false,
						"disableScroll": true
					},
					"meta": {
						"auth": true
					}
				},
				{
					"path": "appoint/appoint-food-details",
					"style": {
						"navigationBarTitleText": "菜品详情"
					},
					"meta": {
						"auth": true
					}
				},
				{
					"path": "appoint/appint-set-meal-food-details",
					"style": {
						"navigationBarTitleText": "菜品详情"
					},
					"meta": {
						"auth": true
					}
				},
				{
					"path": "appoint/food-reasonable-collocation",
					"style": {
						"navigationBarTitleText": "合理搭配"
					},
					"meta": {
						"auth": true
					}
				},
				{
					"path": "appoint/nutrition-details",
					"style": {
						"navigationBarTitleText": "营养元素"
					},
					"meta": {
						"auth": true
					}
				},
				{
					"path": "meal_public/meal_code",
					"style": {
						"navigationBarTitleText": "取餐码"
					},
					"meta": {
						"auth": true
					}
				},
				{
					"path": "appoint/confirm_order",
					"style": {
						"navigationBarTitleText": "提交订单"
					},
					"meta": {
						"auth": true
					}
				},
				{
					"path": "appoint/choose_cupboard",
					"style": {
						"navigationBarTitleText": "请选择餐柜地址"
					},
					"meta": {
						"auth": true
					}
				},
				{
					"path": "select/select_diner",
					"style": {
						"navigationBarTitleText": "预约就餐信息",
						"navigationStyle": "custom"
					},
					"meta": {
						"auth": true
					}
				},
				{
					"path": "select/select_address",
					"style": {
						"navigationBarTitleText": "选择配送地址"
					},
					"meta": {
						"auth": true
					}
				},
				{
					"path": "payment/payment",
					"style": {
						"navigationBarTitleText": "支付"
					},
					"meta": {
						"auth": true
					}
				},
				{
					"path": "payment/payment_result",
					"style": {
						"navigationBarTitleText": "支付结果"
					},
					"meta": {
						"auth": true
					}
				},
				{
					"path": "payment/payment_code",
					"style": {
					    "navigationBarTitleText": "付款码"
					},
					"meta": {
						"auth": true
					}
				},
				{
					"path": "payment/meituan_payment_result",
					"style": {
						"navigationBarTitleText": "待支付"
					},
					"meta": {
						"auth": true
					}
				},
				{
					"path": "payment/meituan_payment",
					"style": {
						"navigationBarTitleText": "支付结果"
					},
					"meta": {
						"auth": true
					}
				},
				{
					"path": "intention_food/intention_food",
					"style": {
						"navigationBarTitleText": "意向菜谱",
						"navigationStyle": "custom"
					},
					"meta": {
						"auth": true
					}
				},
				{
					"path": "intention_food/choice_org",
					"style": {
						"navigationBarTitleText": "选择组织",
						"navigationStyle": "custom"
					},
					"meta": {
						"auth": true
					}
				},
				{
					"path": "recharge/select_wallet",
					"style": {
						"navigationBarTitleText": "选择钱包"
					},
					"meta": {
						"auth": true
					}
				},
				{
					"path": "recharge/recharge",
					"style": {
						"navigationBarTitleText": "充值"
					},
					"meta": {
						"auth": true
					}
				},
				{
					"path": "recharge/recharge_status",
					"style": {
						"navigationBarTitleText": "充值"
					},
					"meta": {
						"auth": true
					}
				},
				{
					"path": "recharge/abc_recharge_code",
					"style": {
						"navigationBarTitleText": "农行充值"
					},
					"meta": {
						"auth": true
					}
				},
				{
					"path": "switch_items/switch_items",
					"style": {
						"navigationBarTitleText": "切换项目"
					},
					"meta": {
						"auth": true
					}
				},
				{
					"path": "switch_items/search_canteen",
					"style": {
						"navigationBarTitleText": "搜索食堂"
					},
					"meta": {
						"auth": true
					}
				},
				{
					"path": "switch_items/add_items",
					"style": {
						"navigationBarTitleText": "添加项目"
					},
					"meta": {
						"auth": true
					}
				},
				{
					"path": "switch_items/switch_phone",
					"style": {
						"navigationBarTitleText": "切换手机号"
					},
					"meta": {
						"auth": true
					}
				},
				{
					"path": "address/address_manage",
					"style": {
						"navigationBarTitleText": "地址管理"
					},
					"meta": {
						"auth": true
					}
				},
				{
					"path": "address/add_address",
					"style": {
						"navigationBarTitleText": "新增地址"
					},
					"meta": {
						"auth": true
					}
				},
				{
					"path": "address/choice_address",
					"style": {
						"navigationBarTitleText": "选择地址",
						"enablePullDownRefresh": false
					}
				},
				// {
				// 	"path": "discount_center/discount_center",
				// 	"style": {
				// 		"navigationBarTitleText": "优惠中心"
				// 	},
				// 	"meta": {
				// 		"auth": true
				// 	}
				// },
				// {
				// 	"path": "discount_center/exchange",
				// 	"style": {
				// 		"navigationBarTitleText": "优惠中心"
				// 	},
				// 	"meta": {
				// 		"auth": true
				// 	}
				// },
				{
					"path": "dining_apply/dining_apply",
					"style": {
						"navigationBarTitleText": "就餐申请"
					},
					"meta": {
						"auth": true
					}
				},
				{
					"path": "dining_apply/dining_status",
					"style": {
						"navigationBarTitleText": "就餐状态"
					},
					"meta": {
						"auth": true
					}
				},
				{
					"path": "meal_report/user_meal_report",
					"style": {
						"navigationBarTitleText": "我的报餐"
					}
				},
				{
					"path": "meal_report/meal_report",
					"style": {
						"navigationBarTitleText": "报餐",
						"navigationStyle": "custom"
					}
				},
				{
					"path": "meal_report/confirm_order",
					"style": {
						"navigationBarTitleText": "提交订单"
					}
				},
				{
					"path": "meal_report/meal_package",
					"style": {
						"navigationBarTitleText": "餐包预订",
						"navigationStyle": "custom"
					}
				},
				{
					"path": "meal_report/meal_package_confirm",
					"style": {
						"navigationBarTitleText": "提交订单"
					}
				},
				{
					"path": "meal_report/meal_package_resume",
					"style": {
						"navigationBarTitleText": "恢复就餐"
					}
				},
				{
					"path": "related_personnel/related_personnel",
					"style": {
						"navigationBarTitleText": "关联人员"
					},
					"meta": {
						"auth": true
					}
				},
				{
					"path": "related_personnel/add_personnel",
					"style": {
						"navigationBarTitleText": "添加关联人员"
					},
					"meta": {
						"auth": true
					}
				},
				{
					"path": "related_personnel/authorization_wallet",
					"style": {
						"navigationBarTitleText": "授权钱包"
					},
					"meta": {
						"auth": true
					}
				},
				{
        	"path": "coupon/index",
        	"style": {
        		"navigationBarTitleText": "卡劵中心"
        	}
        },
				{
        	"path": "coupon/my_coupon",
        	"style": {
        		"navigationBarTitleText": "我的卡劵"
        	}
        },
				{
        	"path": "payment/select_coupon",
        	"style": {
        		"navigationBarTitleText": "优惠券"
        	}
        }
			]
		},
		{
			"root": "pages_third",
			"pages": [{
					"path": "scan/payment/payment",
					"style": {
						"navigationBarTitleText": "输入金额",
						"navigationStyle": "custom",
						"disableScroll": true
					},
					"meta": {
						"auth": true
					}
				},
				{
					"path": "scan/payment/result",
					"style": {
						"navigationBarTitleText": "支付结果",
						"navigationStyle": "custom",
						"disableScroll": true
					},
					"meta": {
						"auth": true
					}
				},
				{
					"path": "scan/cupboard/open_cupboard",
					"style": {
						"navigationBarTitleText": "取餐",
						"navigationStyle": "custom",
						"disableScroll": true
					},
					"meta": {
						"auth": true
					}
				},
				{
					"path": "face_collect/face_collect",
					"style": {
						"navigationBarTitleText": "人脸采集",
						"navigationStyle": "custom",
						"disableScroll": true
					},
					"meta": {
						"auth": true
					}
				},
				{
					"path": "face_collect/face_collect_result",
					"style": {
						"navigationBarTitleText": "采集结果",
						"navigationStyle": "custom",
						"disableScroll": true
					},
					"meta": {
						"auth": true
					}
				},
				{
					"path": "face_collect/face_collect_login",
					"style": {
						"navigationBarTitleText": "人脸采集"
					}
				},
				{
					"path": "face_collect/face_collect_zh",
					"style": {
						"navigationBarTitleText": "人脸采集"
					}
				},
				{
					"path": "scan/recharge/user_info",
					"style": {
						"navigationBarTitleText": "账户信息",
						"disableScroll": true,
						"navigationStyle": "custom"
					},
					"meta": {
						"auth": true
					}
				},
				{
					"path": "scan/recharge/recharge",
					"style": {
						"navigationBarTitleText": "账户充值",
						"disableScroll": true,
						"navigationStyle": "custom"
					},
					"meta": {
						"auth": true
					}
				},
        {
        	"path": "scan/recharge/result",
        	"style": {
        		"navigationBarTitleText": "充值结果",
        		"disableScroll": true,
						"navigationStyle": "custom"
        	},
        	"meta": {
        		"auth": true
        	}
        },
        {
        	"path": "car_payment/car_payment",
        	"style": {
        		"navigationBarTitleText": "支付"
        	}
        },
        {
        	"path": "car_payment/car_payment_result",
        	"style": {
        		"navigationBarTitleText": "支付结果"
        	}
        },
				{
        	"path": "customer_tools/index",
        	"style": {
        		"navigationBarTitleText": "快捷工具"
        	}
        },
				{
					"path": "face_collect/abc_face_gather",
					"style": {
						"navigationBarTitleText": "人脸采集",
						"navigationStyle": "custom"
					},
					"meta": {
						"auth": true
					}
				},
				{
					"path": "voip/index",
					"style": {
						"navigationBarTitleText": "视频公话"
					},
					"meta": {
						"auth": true
					}
				},
				{
					"path": "voip/purchase_details",
					"style": {
						"navigationBarTitleText": "视频公话"
					},
					"meta": {
						"auth": true
					}
				},
				{
					"path": "voip/package_purchase",
					"style": {
						"navigationBarTitleText": "套餐购买"
					},
					"meta": {
						"auth": true
					}
				},
				{
					"path": "voip/payment_result",
					"style": {
						"navigationBarTitleText": "支付结果",
						"disableScroll": true
					},
					"meta": {
						"auth": true
					}
				}
			]
		},
		{
			"root": "pages_common",
			"pages": [{
				"path": "jiaofei_center/jiaofei_center",
				"style": {
					"navigationBarTitleText": "缴费中心"
				}
			}, {
				"path": "jiaofei_center/jiaofei_list",
				"style": {
					"navigationBarTitleText": ""
				}
			}, {
				"path": "jiaofei_center/jiaofei_pay",
				"style": {
					"navigationBarTitleText": "缴费"
				}
			}, {
				"path": "jiaofei_center/jiaofei_refund",
				"style": {
					"navigationBarTitleText": "退款申请"
				}
			}, {
				"path": "jiaofei_center/jiaofei_detail",
				"style": {
					"navigationBarTitleText": "订单详情"
				}
			}, {
				"path": "jiaofei_center/jiaofei_replace",
				"style": {
					"navigationBarTitleText": "代缴费"
				}
			}, {
				"path": "jiaofei_center/jiaofei_electronic_certificate",
				"style": {
					"navigationBarTitleText": "电子凭证"
				}
			}, {
				"path": "control_attendance/control_attendance",
				"style": {
					"navigationBarTitleText": "门禁考勤"
				}
			},
			{
				"path": "control_attendance/access_record",
				"style": {
					"navigationBarTitleText": "进出记录"
				}
			},
			{
				"path": "control_attendance/attendance_record",
				"style": {
					"navigationBarTitleText": "考勤记录"
				}
			},
			{
				"path": "car_admin/car_order",
				"style": {
					"navigationBarTitleText": "车辆管理"
				}
			},
			{
				"path": "car_admin/car_select",
				"style": {
					"navigationBarTitleText": "切换车辆"
				}
			},
			{
				"path": "meal_apply/apply_list",
				"style": {
					"navigationBarTitleText": "申请列表",
					"navigationStyle": "custom"
				}
			},
			{
				"path": "meal_apply/add_meal_apply",
				"style": {
					"navigationBarTitleText": "发起申请"
				}
			},
			{
				"path": "meal_apply/apply_meal_code",
				"style": {
					"navigationBarTitleText": "访客码"
				}
			},
			{
				"path": "meal_apply/apply_payment_result",
				"style": {
					"navigationBarTitleText": "支付结果"
				}
			},
			{
				"path": "question/question_list",
				"style": {
					"navigationBarTitleText": "调查问卷",
					"navigationStyle": "custom"
				}
			},
			{
				"path": "question/main_question",
				"style": {
					"navigationBarTitleText": "调查问卷",
					"navigationStyle": "custom"
				}
			},
			{
				"path": "question/doing_question",
				"style": {
					"navigationBarTitleText": "调查问卷",
					"navigationStyle": "custom"
				}
			},
			{
				"path": "question/question_result",
				"style": {
					"navigationBarTitleText": "调查问卷"
				}
			},
			{
				"path": "supervision/supervision_democratic",
				"style": {
					"navigationBarTitleText": "民住反馈",
					"navigationStyle": "custom"
				}
			}
		]
		},
		{
			"root": "pages_order",
			"pages": [
				{
					"path": "review/index",
					"style": {
						"navigationBarTitleText": "审核查询",
						"navigationStyle": "custom"
					},
					"meta": {
						"auth": true
					}
				},
				{
					"path": "review/detail",
					"style": {
						"navigationBarTitleText": "审核详情"
					},
					"meta": {
						"auth": true
					}
				},
				{
					"path": "review/apply/index",
					"style": {
						"navigationBarTitleText": "取消订单申请"
					},
					"meta": {
						"auth": true
					}
				},
				{
					"path": "review/apply/select_meal",
					"style": {
						"navigationBarTitleText": "选择餐段",
						"navigationStyle": "custom"
					},
					"meta": {
						"auth": true
					}
				},
				{
					"path": "food_error/error_feedback",
					"style": {
						"navigationBarTitleText": "菜品纠正"
					}
				},
				{
					"path": "food_error/add_custom_food",
					"style": {
						"navigationBarTitleText": "自定义食物"
					}
				},
				{
					"path": "invoice/apply",
					"style": {
						"navigationBarTitleText": "开电子发票"
					},
					"meta": {
						"auth": true
					}
				},
				{
					"path": "invoice/apply_other",
					"style": {
						"navigationBarTitleText": "更多信息"
					},
					"meta": {
						"auth": true
					}
				},
				{
					"path": "invoice/list",
					"style": {
						"navigationBarTitleText": "开票记录"
					},
					"meta": {
						"auth": true
					}
				},
				{
					"path": "invoice/details",
					"style": {
						"navigationBarTitleText": "发票详情"
					},
					"meta": {
						"auth": true
					}
				},
				{
					"path": "invoice/select_order",
					"style": {
						"navigationBarTitleText": "按订单开发票"
					},
					"meta": {
						"auth": true
					}
				},
        {
        	"path": "order/order_lists",
        	"style": {
        		"navigationBarTitleText": "我的订单",
        		"disableScroll": true
        	},
        	"meta": {
        		"auth": true
        	}
        },
        {
        	"path": "order/order_detail",
        	"style": {
        		"navigationBarTitleText": "订单详情"
        	},
        	"meta": {
        		"auth": true
        	}
        },
        {
        	"path": "order/evaluate",
        	"style": {
        		"navigationBarTitleText": "评价"
        	},
        	"meta": {
        		"auth": true
        	}
        },
        {
        	"path": "order/refund",
        	"style": {
        		"navigationBarTitleText": "退款"
        	},
        	"meta": {
        		"auth": true
        	}
        },
				{
        	"path": "order/refund_details",
        	"style": {
        		"navigationBarTitleText": ""
        	},
        	"meta": {
        		"auth": true
        	}
        }
			]
		},
    {
    	"root": "pages_member",
    	"pages": [
    		{
    			"path": "member_center/VIP_page",
    			"style": {
    				"navigationBarTitleText": "会员中心",
            // "navigationBarBackgroundColor":"#303138",
            // "navigationBarTextStyle":"white",
						"navigationStyle": "custom"
    			},
    			"meta": {
    				"auth": false	
    			}
    		},
        {
    			"path": "member_center/growth_score_detail",
    			"style": {
    				"navigationBarTitleText": "成长分明细",
            "navigationBarBackgroundColor":"#303138",
            "navigationBarTextStyle":"white"
    			},
    			"meta": {
    				"auth": true
    			}
    		}
      ]
    },
    {
    	"root": "pages_nutrition_circle",
    	"pages": [
    		{
    			"path": "diet_recommended/index",
    			"style": {
    				"navigationBarTitleText": "饮食推荐",
						"onReachBottomDistance": 20
    			},
    			"meta": {
    				"auth": true
    			}
    		},
				{
    			"path": "diet_recommended/recipe_list",
    			"style": {
    				"navigationBarTitleText": "食谱列表",
						"onReachBottomDistance": 20
    			},
    			"meta": {
    				"auth": true
    			}
    		},
				{
					"path": "diet_recommended/three_meal_recommended",
					"style": {
						"navigationBarTitleText": "三餐推荐"
					},
					"meta": {
						"auth": true
					}
				},
				{
					"path": "diet_recommended/three_meal_detail",
					"style": {
						"navigationBarTitleText": "三餐详情"
					},
					"meta": {
						"auth": true
					}
				}
      ]
    },
		{
			"root": "pages_food_ingredient",
			"pages": [
				{
					"path": "food_ingredient/foodIngredient",
					"style": {
						"navigationBarTitleText": "查食材"
					},
					"meta": {
						"auth": true
					}
				},
				{
					"path": "food_ingredient/foodIngredientDetail",
					"style": {
						"navigationBarTitleText": "食材详情"
					},
					"meta": {
						"auth": true
					}
				}
			]
		}
	],
	"preloadRule": {
		// "pages_health": {
		// 		"network": "all",
		// 		"packages": ["__APP__"]
		// }
		// "pagesB/detail/detail": {
		// 		"network": "all",
		// 		"packages": ["pagesA"]
		// }
	},
	"tabBar": {
		"custom": true,
		"color": "#C1C4C3",
		"selectedColor": "#11E69E",
		"borderStyle": "black",
		"backgroundColor": "#ffffff",
		"height": "0px",
		"list": [{
				"pagePath": "pages/index/index",
				// "iconPath": "static/icons/tab_home.png",
				// "selectedIconPath": "static/icons/tab_home_s.png",
				"text": "首页"
			},
			// {
			// 	"pagePath": "pages/nutrition_circle/nutrition_circle",
			// 	"iconPath": "static/icons/nutrition_circle_table.png",
			// 	"selectedIconPath": "static/icons/nutrition_circle_table_s.png",
			// 	"text": "营养圈"
			// },
			// {
			// 	"pagePath": "pages/discovery/index",
			// 	"iconPath": "static/icons/discovery1.png",
			// 	"selectedIconPath": "static/icons/discovery.png",
			// 	"text": "发现"
			// },
			// {
			// 	"pagePath": "pages/photo_recognition/photo_recognition",
			// 	"iconPath": "static/icons/tab_photo.png",
			// 	"selectedIconPath": "static/icons/tab_photo_s.png",
			// 	"text": "饮食记录"
			// },
			{
				"pagePath": "pages/health_center/health_center",
				// "iconPath": "static/icons/tab_healthy.png",
				// "selectedIconPath": "static/icons/tab_healthy_s.png",
				"text": "健康中心"
			},
			{
				"pagePath": "pages/user/user",
				// "iconPath": "static/icons/tab_user.png",
				// "selectedIconPath": "static/icons/tab_user_s.png",
				"text": "个人中心"
			}
		]
	},
	"globalStyle": {
		"navigationBarTextStyle": "black",
		"navigationBarTitleText": "朴食健康",
		"navigationBarBackgroundColor": "#FFFFFF",
		"backgroundColor": "#FFFFFF"
	},
	"easycom": {}
}
