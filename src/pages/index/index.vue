<template>
  <!-- psbc 邮储银行 -->
  <view :style="theme.style" :class="['index', platform === 'psbc' ? 'p-t-60' : '']" @touchmove="popupShow = true" @touchend="popupShow = false">
    <!-- #ifdef H5 -->
    <view v-if="platform === 'psbc'" class="navbar">
      <u-navbar title="朴食健康" :fixed="true" @leftClick="exitApp" @rightClick="closeAllAPP">
        <view class="u-nav-slot" slot="right">
          <u-icon name="close-circle" size="40rpx"></u-icon>
        </view>
        <!-- <view class="u-nav-slot" slot="right">
          <u-icon name="home" size="36rpx"></u-icon>
          <u-line direction="column" :hairline="false" length="36" margin="0 8px"></u-line>
          <u-icon name="close-circle" size="36rpx"></u-icon>
        </view> -->
      </u-navbar>
    </view>
    <view v-else class="navbar" :style="{ opacity: navBg }">
      <u-navbar left-icon-size="0" title="朴食健康" :fixed="true"></u-navbar>
    </view>
    <!-- #endif -->
    <view class="banner">
      <u-swiper
        height="100%"
        :list="Number(banner.length) ? banner : []"
        key-name="image"
        interval="5000"
        :indicator="false"
        :autoplay="true"
        indicator-mode="dot"
        @click="clickBanner"
      ></u-swiper>
    </view>
    <view class="main-content">
      <!-- 用户信息 -->
      <view class="user-info card flex col-center">
        <view class="info-content flex-1 flex col-center">
          <!-- <u-avatar  :src="userInfo.headimgurl?userInfo.headimgurl:themeImgPath.img_avatar"></u-avatar> -->
          <u-avatar
            :src="
              userInfo.card_face_url
                ? userInfo.card_face_url
                : themeImgPath.img_avatar
            "
          ></u-avatar>
          <view class="info-name flex-1 m-l-12">
            <view class="user-info-l">
              <view v-if="isBindProject && userInfo.person_no" class="f-w-500 m-r-10 black line-1">{{ userInfo.card_name }}</view>
              <view v-else class="card-title-not-project">{{ '朴食健康用户' }}</view>
              <view v-if="isBindProject && userInfo.person_no" class="card-title">{{ isBindProject ? userInfo.company_name : '暂无项目' }}</view>
              <!-- <view class="tag mini" v-if="userInfo.card_name">普通会员</view> -->
            </view>
            <view
              v-if="isBindProject && userInfo.person_no"
              class="change-bind-project img-filter"
              @click="$miRouter.push('/pages_bundle/switch_items/switch_items')"
            >
              <u-image :src="themeImgPath.img_change" width="60px" height="25px" ></u-image>
            </view>
            <view v-else class="change-bind-project">
              <u-image
                @click="$miRouter.push('/pages_bundle/switch_items/switch_items')"
                :src="themeImgPath.img_choose_project"
                width="120px"
                height="25px"
              ></u-image>
            </view>
            <!-- <view class="muted mini line-1">欢迎使用朴食健康小程序系统</view> -->
          </view>
        </view>
        <!-- <router-link class="m-l-26" to="/pages_bundle/payment/payment_code">
          <view class="info-paycode text-center">
            <image class="icon m-b-10" :src="themeImgPath.img_pay_code"></image>
            <view class="muted mini">付款码</view>
          </view>
        </router-link> -->
      </view>
      <!-- 菜单 -->
      <view v-if="isBindProject && userInfo.person_no" class="menu card m-t-20">
        <view class="menu-header flex row-between">
          <!-- <view class="card-title card-title-project">{{ isBindProject ? userInfo.company_name : '暂无项目' }}</view> -->
          <!-- <view class="xs muted flex flex-center" @click="$miRouter.push('/pages_bundle/switch_items/switch_items')">
            <text>{{ isBindProject ? '切换' : '请先绑定项目' }}</text>
            <u-icon color="inherit" name="arrow-right" size="24rpx"></u-icon>
          </view> -->
        </view>
        <!-- 中部多项功能区域 -->
        <view>
          <view class="menu-lists">
            <view class="swiper-card">
              <swiper
                class="swiper-wrapper"
                :style="{ height: menuHeight + 'px' }"
                :indicator-dots="false"
                :autoplay="false"
                @change="changeSwiper"
              >
                <swiper-item v-for="(row, i) in menuList" :key="i">
                  <view class="swiper-item-v flex flex-wrap">
                    <view
                      v-for="(item, index) in row"
                      :class="['menu-item flex flex-col col-center', index < 5 ? 'm-b-30' : '']"
                      :key="index"
                      @click="handleClickMenu(item)"
                    >
                      <view :class="[item.noFilter ? '' : 'img-filter']">
                        <u-image width="60rpx" height="60rpx" :src="item.icon"></u-image>
                      </view>
                      <view class="m-t-16 xs">{{ item.name }}</view>
                      <view v-if="item.type === 'account_info' && showReceiveSubsidy || item.type === 'shop_feedback' && noticeBadge.shop_feedback || item.type === 'voip' && noticeBadge.voip_message" class="menu-item-point"></view>
                    </view>
                  </view>
                </swiper-item>
              </swiper>
              </view>
              <!-- 指示器 -->
              <view class="swiper-dot-wrapper flex flex-center">
                <view v-for="(j, k) in menuList" :key="k" :class="['swiper-dot', currentSwiper === k ? 'active' : '']"></view>
              </view>
            </view>
          </view>
          <!--
            <view v-else-if="!isAbc" class="card flex flex-col flex-center">
            <view class="xs muted">请先绑定项目，才可使用食堂线上服务</view>
            <view class="m-t-30 flex" @click="$miRouter.push('/pages_bundle/switch_items/add_items')">
              <u-button
                type="primary"
                shape="circle"
                icon="plus"
                text="绑定食堂"
                size="mini"
                color="linear-gradient(90deg, #11E69E 0%, #11E6C5 100%)"
                :customStyle="customBtnStyle"
              ></u-button>
            </view>
          </view>
          -->
      </view>
      <!-- 通知 -->
      <router-link v-if="notice && isBindProject && userInfo.person_no" to="/pages_info/news/project_news">
        <view class="notice m-t-20 flex">
          <view class="notice-title flex col-center">
            <image class="notice-system m-r-6" :src="themeImgPath.img_notice_system"></image>
            <view class="notice-tag mini">通知</view>
          </view>
          <!-- <view class="flex-1 flex col-center p-l-16">
						<u-tag text="最新" plain size="mini"></u-tag>
						<view class="f-w-500 line-1 flex-1 xs hidden">
							食堂开业送积分，最低200起送！
						</view>
						<u-icon :color="$vars.color_text_secondary" name="arrow-right" size="24rpx"></u-icon>
					</view> -->
          <u-notice-bar icon=" " mode="link" :text="notice" fontSize="26rpx" bgColor="transparent" color="#101010" direction="column"></u-notice-bar>
          <view v-if="marketingSetting && marketingSetting.unread_notice_count" class="notice-num">{{marketingSetting.unread_notice_count}}</view>
				</view>
			</router-link>
      <!-- 新增代码xbh --- 钱包余额 -->
      <view v-if="isBindProject && userInfo && userInfo.person_no" class="wallet-balance">
        <view class="wallet-balance-content">
          <view class="wallet-balance-content-l">
            <view class="content-t">
              <text class="t-l">钱包余额</text>
              <!-- 提醒功能暂时不上线，后期上线时解除注释 -->
              <!-- <text class="t-r">(余额较低，建议充值)</text> -->
            </view>
            <view class="content-m">
              <text class="content-mid-l">￥{{ userWallet }}</text>
            </view>
          </view>
          <router-link class="m-l-26" to="/pages_bundle/payment/payment_code">
            <view class="wallet-balance-content-r">
            <u-button
              type="primary"
              shape="circle"
              text="付款码"
              size="mini"
              :custom-style="memberBtnStyle"
              :color="variables.bgLinearGradient1"
              bg-primary
            ></u-button>
            </view>
          </router-link>
        </view>
        <!--20231106产品宏宇要求隐藏-->
        <!-- <view v-if="this.isLastOrder" class="wallet-balance-footer">
          <view class="wallet-footer-item">
            {{ this.lastOrder.date + this.lastOrder.order_type_alias }}
            <text>{{ this.lastOrder.order_type === 0 || this.lastOrder.order_type === 2 ? '-' : '+' }}{{ '￥' + this.lastOrder.fee  }}</text>
          </view>
        </view> -->
      </view>
      <!-- 推广图 -->
      <generalization-map v-if="isGetGeneralizationMapShow&&hasGeneralizationMap && userInfo.person_no" ref="generalizationMap" uiType="front_page" :key="refresh" class="m-t-20 m-b-20" @transfer="getList" :pageType="'index'"></generalization-map>
      <!-- 新增代码xbh --- 订单数据 -->
      <view v-if="isBindProject && userInfo.person_no && nowMealOrder.length > 0" class="meal-order">
        <view class="content-t">
          <text class="t-text">
            您有
            <text class="order-num">{{ nowMealOrder.length }}</text>
            笔订单已开餐，点击出示核销码
          </text>
        </view>
          <swiper class="meal-swiper" :autoplay="false" :circular="true" :current="currentIndex" @change="changeIndex">
          <swiper-item v-for="(item, index) in nowMealOrder" :key="index" @click="getOrderQrcode">
            <view class="meal-order-content">
              <view class="content-main">
                <view class="main-card">
                  <view class="main-card-l">
                    <view>
                      <text class="meal-type">{{ item.meal_type_alias }}</text>
                      <text class="meal-name">{{ item.org_name }}</text>
                    </view>
                    <view class="meal-num">{{ item.trade_no }}</view>
                  </view>
                  <view class="main-card-r img-filter">
                    <u-image :src="themeImgPath.img_report_right" width="20px" height="20px"></u-image>
                  </view>
                </view>
              </view>
            </view>
          </swiper-item>
        </swiper>
      </view>
      <!-- 自注册审批列表 -->
      <view v-if="autoRegisterList && autoRegisterList.length > 0" class="auto-register">
        <view v-for="approve in autoRegisterList" :key="approve.id" class="register-item m-t-20">
          <view class="register-title flex row-between">
            <text class="register-company">{{ approve.company_name }}</text>
            <text class="register-status red">{{ approve.approve_status_alias }}</text>
          </view>
          <view class="register-content">
            <view class="register-content-text">申请时间：{{ approve.create_time | date('yyyy年mm月dd日 hh:MM:ss') }}</view>
            <view class="register-content-text" v-if="approve.approve_time">审批时间：{{ approve.approve_time | date('yyyy年mm月dd日 hh:MM:ss') }}</view>
            <view class="register-content-text">人员编号：{{ approve.person_no }}</view>
            <view class="register-content-text">姓名：{{ approve.name }}</view>
            <view class="register-content-text" v-if="approve.approve_status === 'REJECT'">拒绝说明：{{ approve.reject_reason }}</view>
          </view>
          <view class="register-footer flex row-right">
            <u-button
              v-if="approve.approve_status === 'REJECT'"
              :custom-style="{
                width: '164rpx',
                minWidth: '110rpx',
                height: '50rpx',
                lineHeight: '50rpx',
                fontSize: '26rpx',
                letterSpacing: '2rpx',
                color: '#fff',
                margin: '0'
              }"
              :color="variables.colorPrimary"
              bg-primary
              @click="approveAgainHandle(approve)"
            >
             再次申请
            </u-button>
          </view>
        </view>
      </view>
       <!-- 饮食报告  目前不要了 先隐藏-->
      <!-- <view class="diet-report m-t-20" v-if="segmentNot">
        <view class="diet-report-top flex">
          <view class="card-title flex flex-center">
            <view class="p-r-10 f-w-600 md meal-report-title">饮食报告</view>
            <u-modal :show="dietShow" title="饮食报告" @confirm="dietShow = false" confirmColor="#11E69E">
              <view class="slot-content">
                <text>
                  饮食报告显示当天的饮食信息，根据用户实际摄入进行营养分析，根据数据来源提供饮食图片和菜品数据，具体信息请查看报告内容。
                  <br />
                  饮食报告仅对一日三餐的饮食摄入进行记录分析，额外加餐的营养数据不计算在内，如需了解整天的摄入情况，请前往‘营养分析’功能查看
                </text>
              </view>
            </u-modal>
            <u-icon name="question-circle" color="#999" size="32rpx" @click="dietShow = true"></u-icon>
          </view>
          <view class="xs muted flex flex-center">
            <view @click="moreReports" class="xs">更多报告</view>
            <u-icon color="inherit " name="arrow-right" size="24rpx"></u-icon>
          </view>
        </view>
        <view v-if="!isShowReport">
          <view v-if="indexNutrientData && Object.keys(indexNutrientData) && Object.keys(indexNutrientData).length">
            <view class="muted flex flex-center f-s-24 m-t-50">记录完整数据更有效分析饮食健康</view>
            <view class="diet-report-but flex flex-center">
              <view>
                <u-button
                  @click="manualSelection"
                  :custom-style="customStyleRecord"
                  :color="variables.bgLinearGradient1"
                  bg-primary
                >
                  手动记录
                </u-button>
              </view>
            </view>
          </view>
          <view v-else>
            <view class="muted flex flex-center f-s-24 m-t-50">完善个人信息，解锁健康生活新方式</view>
            <view class="diet-report-but flex flex-center">
              <view>
                <u-button
                  @click="$miRouter.pushTab('/pages/health_center/health_center')"
                  :custom-style="customStyleRecord"
                  :color="variables.bgLinearGradient1"
                  bg-primary
                >
                  <text>立即前往</text>
                  <u-icon name="arrow-right" color="#fff" size="26"></u-icon>
                </u-button>
              </view>
            </view>
					</view>
        </view>
        <view v-if="isShowReport">
          <view class="report-center flex row-between" @click="jumpDietary(reportDetails.id)">
            <view class="report-center-left m-l-30">
              <view class="report-center-right-top md">{{ reportDetails.mealTypeAlias }} {{ reportDetails.createTime }}</view>
              <view class="report-center-right-button muted sm">热量摄入: {{ reportDetails.energyKcal | toFixedtwo }}kcal</view>
            </view>
            <view class="report-center-right flex flex-center m-r-30">
              <image :src="reportDetails.foodSceneImages"></image>
              <view class="report-center-right-num mini flex flex-center">{{ reportDetails.imgNum }}张</view>
            </view>
          </view>
          <view class="divider"></view>
          <view class="grey nr flex flex-center m-t-25 m-b-30">查看实际摄入的分析情况</view>
        </view>
      </view> -->
      <!-- 食谱计划 -->
      <menu-plan v-if="isBindProject && userInfo.person_no" ref="menuPlan"></menu-plan>

      <!-- 饮食能量 -->
				<view class="dietary-energy card m-t-20" v-if="false">
					<!-- 判断是否有健康档案 -->
					<view v-if="userInfo.healthy_info">
						<view class="flex row-between">
							<view class="card-title flex flex-center">
								<text class="p-r-10">饮食能量</text>
								<!-- #ifdef MP-ALIPAY -->
								<u-icon name="reload" color="inherit" size="32rpx" @click="getIndexNutrient"></u-icon>
								<!-- #endif -->
							</view>
							<view class="xs muted flex flex-center" @click="$miRouter.push('/pages_health/healthy/diet_healthy/index')">
								<text>分析</text>
								<u-icon color="inherit" name="arrow-right" size="24rpx"></u-icon>
							</view>
						</view>
						<view class="energy m-t-30">
							<view class="energy flex flex-center m-t-30" v-if="indexNutrientData.energy_egg && indexNutrientData.energy_egg.length">
								<view
									class="energy-item flex flex-col flex-center"
									:style="{ transform: `translateY(${(1 - Math.random()) * 20}rpx)` }"
									v-for="(item, index) in indexNutrientData.energy_egg.slice(0, 4)"
									:key="index"
									@click="getClearEnergyEgg(item, index)"
								>
									<view class="energy-ball flex flex-col flex-center" :style="{'background-image': themeImgPath.img_energy_ball}">
										<text class="xs primary m-t-30">+{{ item.energy_kcal }}</text>
									</view>
									<text class="energyEggName muted mini m-t-10">{{ nameFormat(item.name,5) }}</text>
								</view>
							</view>
						 <!-- <view class="flex flex-center" @click="pathpHoto" v-else>
									<view class="add_energy p-l-40 p-r-40 p-t-10 p-b-10">添加能量</view>
							</view> -->
						</view>
						<view class="flex row-between xs m-t-40">
							<text class="f-w-500">今日摄入情况</text>
							<text class="muted">单位：kcal</text>
						</view>
						<view class="energy-formula m-t-20 flex col-center" v-if="indexNutrientData && Object.keys(indexNutrientData) && Object.keys(indexNutrientData).length">
							<circle-progress
								:width="130"
								:border-width="3"
								active-color="#40DFD6"
								inactive-color="#F4F9F7"
								:percent="100"
								title="可摄入"
								:subtitle="indexNutrientData.need_energy_kcal"
							>
							</circle-progress>
						 <view class="muted flex-1 text-center">-</view>
						 <circle-progress
								:width="130"
								:border-width="3"
								active-color="#40DFD6"
								inactive-color="#F4F9F7"
								:percent="indexNutrientData.index_energy_kcal_percent"
								title="已摄入"
								:subtitle="indexNutrientData.use_energy_kcal"
							>
							</circle-progress>
						 <view class="muted flex-1 text-center">+</view>
						 <circle-progress
								:width="130"
								:border-width="3"
								active-color="#F8A63C"
								inactive-color="#F4F9F7"
								:percent="indexNutrientData.use_sports_energy_kcal_percent"
								title="运动消耗"
								:subtitle="indexNutrientData.use_sports_energy_kcal"
							>
							</circle-progress>
						 <view class="muted flex-1 text-center">=</view>
						 <circle-progress
								:width="130"
								:border-width="3"
								active-color="#11E69E"
								inactive-color="#F4F9F7"
								:percent="indexNutrientData.can_energy_kcal_percent"
								title="还能摄入"
								:subtitle="indexNutrientData.can_energy_kcal"
							>
							</circle-progress>
						</view>
						<!-- 热量差 -->
						<view class="caloric-difference muted xs">
							<!-- <text class="iconfont icon-hot" style="font-size: 30rpx;"></text> -->
							热量差：<text class="black p-r-10">{{indexNutrientData.heat?indexNutrientData.heat:""}}kcal</text>相当于燃烧了<text  class="black p-l-10 p-r-10">{{indexNutrientData.axunge?indexNutrientData.axunge:""}}g</text>脂肪
						</view>
					</view>
					<!-- 没有健康档案 -->
					<view class="flex row-between col-center"  v-else>
						<view>
								<view class="xl f-w-500">饮食能量</view>
								<view class="mini muted">完善个人信息，解锁健康生活新方式</view>
						</view>
						<view>
							<u-button
								iconColor="#fff"
								size="mini"
								shape="circle"
								:color="variables.bgLinearGradient1"
								@click="$miRouter.pushTab('/pages/health_center/health_center')">
								<text>立即前往</text>
								<u-icon name="arrow-right" color="#fff" size="26"></u-icon>
							</u-button>
						</view>
					</view>
				</view>
      <!-- 健康测评 身体管理 -->
      <!-- <view class="card-menu m-t-20"> -->
      <!--<view
          class="card-menu__item body m-r-20"
          style="width: 100%;"
          @click="bobyManagementPath"
          v-if="userInfo.h5_context_display"
        >
          <view class="md f-w-500">身体管理</view>
          <view class="muted mini m-t-15">检测个人身体状况</view>
        </view> -->
      <!-- <view class="flex row-between"> -->
      <!--  <view
            class="card-menu__item dietary m-b-10"
            @click="dietaryManagementPath"
          >
            <view class="md f-w-500">饮食推荐</view>
            <view class="muted mini m-t-15">根据个人情况推荐</view>
          </view>
          <view class="card-menu__item healthy" :style="{'background-image': `url(${themeImgPath.img_health_assessment})`}" @click="healthyEvaluationPath">
            <view class="md f-w-500">健康测评</view>
            <view class="muted mini m-t-15">检测个人身体状况</view>
          </view>
        </view> -->
      <!-- </view> -->

      <!-- 健康资讯 -->
      <!--      <view class="health-information card m-t-20" v-if="articleList.length && false">
        <view class="flex row-between">
          <view class="card-title">健康资讯</view>
          <view class="xs muted flex flex-center">
            <text @click="gotoPathDiscovery">更多</text>
            <u-icon color="inherit" name="arrow-right" size="24rpx"></u-icon>
          </view>
        </view>
        <view class="information-lists">
          <view class="information-item flex" v-for="(item, index) in articleList" :key="index" @click="clickArticlePath(item)">
            <view class="flex-1 m-l-14">
              <view class="f-w-500 line-2 m-b-10 text-title">{{ item.title }}</view>
              <view class="xs muted flex row-between">
                <view class="flex text-center row-center">
                  <u-image :src="themeImgPath.img_heat" class="p-t-6 p-r-10" width="20rpx" height="20rpx"></u-image>
                  <view class="">{{ item.read_number }}</view>
                </view>
                <view class="text-center">{{ item.release_time }}</view>
                <view class="flex text-center row-center p-r-30">
                  <text class="m-l-6">{{ item.author }}</text>
                </view>
              </view>
            </view>
            <u-image width="150rpx" height="112rpx" radius="10rpx" :src="item.image"></u-image>
          </view>
        </view>
      </view> -->
      <view class="question-banner" v-if="questionList.length" @click="gotoQuestionList">
        <img :src="themeImgPath.img_investigate_banner" alt="">
      </view>
      <!-- 没有项目点信息 -->
      <view class="muted company_empty"  v-if="!userInfo.person_no">
        暂无用户信息，请先绑定
      </view>
    </view>
    <u-modal title="请选择就餐的组织" :show="buffetShow" :showConfirmButton="false" closeOnClickOverlay>
      <view class="slot-content" style="width: 100%">
        <u-radio-group
          v-model="radioOrgValue"
          placement="column"
          iconPlacement="right"
          class="page__radio-item"
          :activeColor="variables.colorPrimary"
        >
          <scroll-view class="scroll-view_H" scroll-y="true" scroll-left="120" style="width: 100%">
            <u-radio
              v-for="(item, index) in trayOrgList"
              :key="index"
              :customStyle="{ marginBottom: '40rpx', paddingBottom: '30rpx', borderBottom: 'solid 1px #f0f0f0' }"
              :label="item.name"
              :name="item.id"
            ></u-radio>
          </scroll-view>
        </u-radio-group>
        <view class="mask-btn">
          <u-button hover-class="none" :loading="false" @click="buffetShow = false" :custom-style="customStyleBack">
            取消
          </u-button>
          <u-button hover-class="none" :loading="false" @click="clickTrayOrg" :custom-style="customStyleDetermine">确定</u-button>
        </view>
      </view>
    </u-modal>
    <!-- setting -->
    <setting-popup></setting-popup>
    <custom-tabbar type="index"></custom-tabbar>
    <!-- 弹窗 -->
    <popup v-if="isShowPopup"></popup>
    <popup v-if="isShowPopup" type="notice"></popup>
    <popup v-if="isShowPopup" type="message"></popup>
    <!-- 周报弹窗 -->
    <u-popup class="weeklyPopup" :show="isShowWeeklyReport" mode="center" border-radius="14" closeOnClickOverlay @close="isShowWeeklyReport = false">
      <view class="flex flex-col col-center">
        <view class="weeklyNew" :style="{'background-image': `url(${themeImgPath.img_tcbg})`}">
          <view class="newContent flex flex-col col-center">
            <view class="date-range bg-white m-b-12 p-16 f-w-600 xxl">
              {{ dateRange[0] }} ~ {{ dateRange[1] }}
            </view>
            <text class="text m-b-48">
              您有新的饮食健康周报,赶紧来领取吧
            </text>
            <view class="button m-b-20" :style="{'background-image': `url(${themeImgPath.img_weekly_report_button})`}" @click="goToWeeklyReport">
            </view>
            <text class="m-b-80 grey mini">
              已有 {{ userCount }} 人领取
            </text>

          </view>
        </view>
        <u-icon class="m-t-20" name="close-circle" size="64" color="#e4e4e4" @click="isShowWeeklyReport = false"></u-icon>
      </view>
    </u-popup>
    <!-- 更新协议 start -->
    <popup-layout
      :show.sync="showUpdateAgreement"
      title="协议更新提示"
      content="为了让你在安全、放心的环境下使用朴食平台，建议你仔细阅读平台相关协议的更新"
      cancel-text="不同意"
      confirm-text="已阅读并同意"
      @confirm="clickAgreementBtn"
      @cancel="clickAgreementBtn"
      :confirmStyle="popupLayoutConfirmStyle"
      :cancelStyle="popupLayoutCancelStyle"
    >
      <view slot="other" class="agreement-text">
        点击阅读
        <text
          :style="{color: variables.colorPrimary}"
          v-for="agreement in updateAgreementList"
          :key="agreement.id"
          @click="gotoAgreementDetail(agreement)"
        >
          《{{ agreement.agreement_type_alias }}》
        </text>
      </view>
    </popup-layout>
    <!-- 更新协议 end -->
    <!-- 刷脸协议 单独出来 -->
    <popup-layout
      :show.sync="showUpdateAgreementFace"
      title="温馨提示"
      content="你签署的刷脸支付服务协议已更新，为了更好的给你提供服务和保障，建议你仔细阅读本协议。若你不同意本协议，将会关闭刷脸支付服务。若你需要重新开通，请前往【人脸采集】开启服务。"
      :showTip="true"
      cancel-text="不同意"
      confirm-text="已阅读并同意"
      @confirm="clickAgreementBtnFace"
      @cancel="clickAgreementBtnFace"
      :confirmStyle="popupLayoutConfirmStyle"
      :cancelStyle="popupLayoutCancelStyle"
    >
      <view slot="tip">
        <view class="red f-w-700 m-t-20">提示：关闭后用户在食堂无法进行刷脸支付!</view>
      </view>
      <view slot="other" class="agreement-text">
        点击阅读
        <text
          :style="{color: variables.colorPrimary}"
          v-for="agreement in updateAgreementFaceList"
          :key="agreement.id"
          @click="gotoAgreementDetail(agreement)"
        >
          《{{ agreement.agreement_type_alias }}》
        </text>
      </view>
    </popup-layout>
    <!-- 订单二维码 -->
    <!-- <popup-layout :show.sync="qrcodePopupShow" :showTitle="false" :showCancel="false" :showConfirm="false" :custom-style="qrcodeCustomStyle">
      <template>
        <uqrcode v-if="qrcode" ref="uQRCode" :text="qrcode" />
      </template>
      <template name=footer>
        <view>123</view>
      </template>
    </popup-layout> -->
    <non-member :showMember="showMember" :content="modalContent" :show-confirm-button="showConfirmbtn" @setShowMember="setShowMember"></non-member>
    <u-popup class="order-qrcode-popup" :show="qrcodePopupShow" :round="10" mode="center" :customStyle="orderPopupStyle">
      <view class="order-qrcode-title">
        <text>取餐码</text>
      </view>
      <view class="order-qrcode-content">
        <uqrcode v-if="qrcode" ref="uQRCode" :text="qrcode" />
        <view class="order-text-remind">出示出餐码扫码核销</view>
      </view>
      <view class="order-qrcode-bottom">
        <u-button :customStyle="btnGetQrcode" @click="closeQrcode">确定</u-button>
      </view>
    </u-popup>
    <floating-popup :floatingPopupShow="floatingPopupShow"></floating-popup>
    <CustomerServicePopup :popupShow="popupShow" />
    <first-tip-dialog :show="showFirstTipDialog" @close="closeFirstTipDialog"/>
  </view>
</template>
<script>
// #ifdef H5
var jweixin = require('jweixin-module')
// #endif
import {
  apiBookingLoginH5Login,
  apiBookingLoginH5LoginTemp,
  getApiWechatCongfigGet,
  getApiBindProjectCardUserList,
  setUserBindProjectPoint,
  getApiPermission,
  getApiBookingUserAgreementSignList,
  getMarketingSettings,
  checkAgreementApi,
  updateAgreementApi,
  getQywechatConfigGet,
  getApiThirdShow,
  getApiMeituanLoginFree,
  apiBookingYideAuthUrl
} from '@/api/app'
import { getApiUserVoipInfo } from '@/api/voip.js'
import { apiGetCouponManageReceiveList } from '@/api/coupon.js'
import { apiGetSurveyInfoList } from '@/api/question'
import { getApiH5BingTray, getApiTrayOrg } from '@/api/buffet'
import { getApiRechargeWalletList, getApiUserWalletList, apiQueryUserinfo, getApiUserSwitchFacepay } from '@/api/user.js'
import { getApiBocPhoneLogin } from '@/api/bocapp.js'
import { getApiArticleList, getApiIndexNutrient, getApiClearEnergyEgg, apiBookingHealthyPopWeeklyReport } from '@/api/healthy.js'
import { apiGetUserUnreceivedSubsidy } from '@/api/wallet.js'
import { mapActions, mapGetters, mapMutations } from 'vuex'
import Cache from '@/utils/cache'
import { getApiBookingUserGetCardUserList, getApiBookingUserGetCanteenList, getApiTakeMealTypeList } from '@/api/reservation'
import { loadSyncFile } from '@/utils/load_fild_file'
// #ifdef H5
const { aplus_queue } = window
import { initSensors } from  '../../js_sdk/firefly-uglify/point'
// #endif
import { isBankAbcClient, getQueryObject, objectToQuery } from '@/utils/util.js'
import { allMenuList } from '@/constants/menu-list'
// import { setCookie } from '@/utils/cookie'
import { exitApp, closeAllAPP } from '@/utils/psbc'
import popupLayout from '@/components/popup/popup-layout'
// import { apiGetBookingHealthyDietReport } from '@/api/diet_report.js'
import { apiBookingZkLaundryGetZkLaundryScheme } from '@/api/third.js'
import checkMember from '@/mixins/checkMember.js'
import { apiCheckEaccountOpen, apiBookingAbcGetOrgBuryInfo, apiAlipayGetFacepassStatusPost } from '@/api/sign.js'
import menuPlan from '../components/menu_plan/index'
import generalizationMap from '@/components/generalization-map/generalization-map'
// 导入加减乘除计算插件
import * as NP from '@/utils/np.js'
// 导入用户订单
import { apiBookingUserOrderGetUserLastOrder, apiBookingUserGetUserOrderSetting } from '../../api/order'
// 导入dayjs
import dayjs from '../../uni_modules/uview-ui/libs/util/dayjs'
import { deepClone, isExternal } from '@/utils/util.js'
import { formateVisitorParams } from '@/utils/util.js'
import { timeFormat } from '@/utils/date.js'
import FloatingPopup from '@/components/floating-popup/floatingPopup.vue'
import face_collect_resultVue from '../../pages_third/face_collect/face_collect_result.vue'
import { apiBookingApproveRegisterListPost, apiBookingApproveRegisterGetInfoPost  } from '@/api/register'
import CustomerServicePopup from '@/components/customer-service-popup/customer-service-popup.vue'
import { getApiRechargeGetSettingsV2 } from '@/api/user.js'
import { setDemoCompany } from '@/utils/demo'
import { getFeedbackReadState } from '@/utils/getNotice'
import FirstTipDialog from "@/components/first-tip-dialog/first-tip-dialog.vue"


export default {
  components: { popupLayout, menuPlan, FloatingPopup, CustomerServicePopup, FirstTipDialog },
  mixins: [checkMember],
  data() {
    return {
      popupShow: false,
      isGetGeneralizationMapShow: false,
      flag: false,
      companyId: '',
      appid: '',
      navBg: 0,
      isBindProject: false,
      // banner: [
      //   {
      //     image:
      //       'https://packer-static-assets.oss-cn-shenzhen.aliyuncs.com/v4_h5/banner/71b0cd57f3714b1eed4c207942f0c64d1666144275981.png'
      //   }
      // ],
      menuList: [],
      allMenuList: allMenuList,
      permissionMenuList: [], // 用户可以看到的图标列表
      articleList: [],
      buffetShow: false, //选择消费点
      radioOrgValue: '',
      customStyleBack: {
        color: this.$variables.colorPrimary,
        width: '47%',
        border: `1px solid ${this.$variables.colorPrimary}`
      },
      customStyleDetermine: {
        backgroundColor: this.$variables.colorPrimary,
        color: '#fff',
        width: '47%',
        border: 'none'
      },
      customStyleRecord: {
        borderRadius: '50rpx',
        width: '200rpx',
        minWidth: '110rpx',
        height: '56rpx',
        lineHeight: '56rpx',
        fontSize: '26rpx',
        letterSpacing: '2rpx',
        color: '#fff'
      },
      memberBtnStyle: {
        width: '140rpx',
        height: '58rpx',
        lineHeight: '58rpx',
        letterSpacing: '4rpx',
        fontSize: '26rpx',
        borderRadius: '50rpx',
        textAlign: 'center'
      },
      // 二维码弹出层样式
      qrcodeCustomStyle: {
        padding: 0
      },
      // 现有订单二维码弹窗内 --- 按钮样式
      btnGetQrcode: {
        color: this.$variables.colorPrimary,
        borderRadius: '0rpx',
        fontSize: '32rpx',
        fontWeight: 'bold',
        letterSpacing: '6rpx',
        height: '90rpx',
        lineHeight: '90rpx',
        borderRadius: '0 0 10rpx 10rpx'
      },
      trayOrgList: [],
      buffetQrcodeUrlData: {}, //托盘扫码进来的参数
      indexNutrientData: {}, //饮食能量
      timer: null,
      customBtnStyle: {
        minWidth: '120rpx',
        height: '60rpx',
        lineHeight: '60rpx'
      },
      isAbc: isBankAbcClient(),
      currentSwiper: 0,
      permissionList: [],
      // notice: '食堂开业送积分，最低200起送！食堂开业送积分，最低200起送！'
      isRefresh: true, // 是否是刷新页面， 默认第一次都是
      showReceiveSubsidy: false, // 是否有待领取补贴
      isShowPopup: false, // 是否显示弹窗
      showUpdateAgreement: false,
      updateAgreementList: [], // 更新的协议列表
      showUpdateAgreementFace: false, // 更新的刷脸协议单独拿出来列表
      updateAgreementFaceList: [], // 更新的刷脸协议单独拿出来列表
      // isShowReport: false, //是否有饮食报告
      // dietShow: false, //食品报告问号弹窗
      // segmentNot: true, //是否在餐段内
      // reportDetails: {
      //   energyKcal: 0,
      //   createTime: '',
      //   mealTypeAlias: '',
      //   imgNum: 0,
      //   id: '',
      //   foodSceneImages: ''
      // }, //饮食报告内容
      dietContent: '', //食品报告问号弹窗内容
      userWallet: '', // 用户钱包总余额
      lastOrder: null, //最近四笔订单
      isLastOrder: false,
      nowMealOrder: [], // 最近预约的订单
      qrcodePopupShow: false, // 订单二维码弹窗
      qrcode: '', // 二维码生成的链接
      currentIndex: 0,
      orderPopupStyle: {
        width: '90%',
      },
      // 用户信息
      userInfoAll: '',
      dateRange: [],
      userCount: 0,
      isShowWeeklyReport: false,
      weeklyNewId: '',
      refresh: 0,
      hasGeneralizationMap: true,
      floatingPopupShow: false,
      // generalizationShow: true,
      questionList: [],
      autoRegisterList: [], // 自注册审批列表
      walletData: {},
      // 弹窗确定框
      popupLayoutConfirmStyle: {},
      popupLayoutCancelStyle: {},
      showFirstTipDialog: false
    }
  },
  computed: {
    ...mapGetters(['userInfo', 'marketingSetting', 'platform', 'codeInfo', 'noPermissions', 'noticeBadge']),
    banner() {
      // 轮播
      let list = []
      if (this.marketingSetting && this.marketingSetting.banner_list) {
        this.marketingSetting.banner_list.forEach(v => {
          if (v.img_url) {
            // 没有url的就不要添加了
            list.push({
              image: v.img_url,
              jump_type: v.jump_type, // 跳转类型inner内部,outsite外部
              jump_url: v.jump_url, // 内部为allMenuList.permission,外部为链接地址
              name: v.name
            })
          }
        })
      } else {
        // 默认
        list.push({
          image:
            'https://packer-static-assets.oss-cn-shenzhen.aliyuncs.com/v4_h5/banner/71b0cd57f3714b1eed4c207942f0c64d1666144275981.png'
        })
      }
      return list
    },
    notice() {
      // 公告
      // is_alter_new_notice表示是否显示公告  20240527 改成显示多条
      if(this.marketingSetting && Reflect.has(this.marketingSetting, 'top_notice_list')) {
        let list = this.marketingSetting.top_notice_list || []
        let newList = []
        list.forEach(item=>{
          newList.push(item.title)
        })
        return newList
      }
      return []
    },
    // 菜单栏高度
    menuHeight() {
      let height = uni.upx2px(160)
      if ((this.menuList[0] && this.menuList[0].length > 5) || this.menuList.length > 1) {
        height = this.language === 'en' ? uni.upx2px(320) : uni.upx2px(268)
      } else {
        height = this.language === 'en' ? uni.upx2px(160) : uni.upx2px(140)
      }
      return height
    }
  },
  watch: {
    userInfo(val) {
      if (val?.company_id) {
        this.isBindProject = true
      } else {
        this.isBindProject = false
      }
    },
    noPermissions(newVal) {
      if (!this.noPermissions && this.$Route.path === '/pages/index/index') {
        this.isPermissions()
      }
    },
    hasGeneralizationMap(newVal) {
      console.log('有新值',newVal)
    },
    // 监听menu动态设置图标
    permissionMenuList: {
      deep: true,
      handler(val) {
        val.map((item, i) => {
          let k = Math.floor(i / 10) // index
          if (!this.menuList[k]) this.menuList[k] = []
          let current = Object.assign({}, item)
          // icon不为base64的和远程地址，则进行地址拼接
          if (current.icon && typeof current.icon === 'string' && !isExternal(current.icon) && current.icon.indexOf('data:image') < 0) {
            current.icon = this.themeImgPath[current.icon]
          }
          this.menuList[k].push(current)
        })
      }
    }
  },
  onReady() {
    // 支付宝是没有者生命周期吗，不会触发
    uni.hideTabBar()
  },
  onLoad(option) {
    setTimeout(function () {
      // onLoad 因为授权回来有问题
      uni.hideTabBar()
    }, 140)
    // #ifdef H5
    this.$nextTick(_ => {
      // 判断下中国邮政储蓄银行不要隐藏顶部
      if (this.platform !== 'psbc' && document.getElementsByTagName('uni-page-head')[0]) {
        document.getElementsByTagName('uni-page-head')[0].style.display = 'none'
      }
    })
    // #endif
    // 游客不能进首页，清除游客数据的影响
    // if (Cache.get('isVisitor')) {
    //   this.SET_SELECT({
    //     key: 'address_info',
    //     data: {}
    //   })
    //   Cache.remove('isVisitor') // 清除游客缓存
    //   uni.removeStorageSync('codeOpenid') // 清除游客openid
    //   uni.removeStorageSync('appidVisitor') // 清除游客appid
    // }
    this.initLogin(option)
  },
  mounted() {
  },
  onShow() {
    // #ifdef H5
    // 友盟埋点
    if (aplus_queue) {
      aplus_queue.push({
        action: 'aplus.sendPV',
        arguments: [
          {
            is_auto: false
          },
          {
            page: 'index'
          }
        ]
      })
    }
    // 如果平台是农行，首页要增加农行埋点
    if (this.platform === 'abc' && this.userInfo?.company_id) {
      this.getOrgBuryBankSetting()
    }
    // #endif
    if (this.userInfo?.company_id) {
      this.isBindProject = true
    } else {
      this.isBindProject = false
    }
    // 显示时再拉取下接口数据
    if (this.userInfo && !this.isRefresh) {
      this.isShowPopup = face_collect_resultVue
      this.getMarketingSettingHandle()
      this.initNutrient()
      this.getAgreementList()
      // this.GetBookingHealthyDietReport()
      this.userInfo.company_id && this.getQuestionList()
      // 加载食谱计划列表
      if (this.$refs.menuPlan) {
        this.$refs.menuPlan.getHealthyIndexMealPlan()
      }
      this.getUserOrder(this.userInfo.company_id, this.userInfo.person_no, this.userInfo.user_id)
      if (this.userInfo.person_no) {
        this.getUserWalletList(this.userInfo.person_no)
      }
      // 弹窗接口 开始show就调用 *****************写的 -- （mtj娜一下到this.userInfo && !this.isRefresh 下面）
      apiBookingHealthyPopWeeklyReport()
      .then(res => {
        if (res.data && res.data.is_weekly_diet_report) {
          this.isShowWeeklyReport = true
          this.dateRange.push(timeFormat(new Date(res.data.start_date), 'mm月dd日'), timeFormat(new Date(res.data.end_date), 'mm月dd日'))
          this.userCount = res.data.receive_count
          this.weeklyNewId = res.data.id
        }
      })
      // 只有未绑定项目是会调接口
      if (!this.userInfo.company_id) {
        // 自注册审批
        this.getAutoRegisterList()
      }
      getFeedbackReadState()
    }
    // if (this.userInfo?.person_no) {
    //   this.getUserSubsidyList() // 获取补贴
    // }
    this.isRefresh = false
    // this.getList()
  },
  destroyed(){
    uni.$off('routerChange')
  },
  methods: {
    ...mapMutations(['SET_SELECT']),
    ...mapActions({
      setUserInfo: 'setUserInfo',
      setLoginUserId: 'setLoginUserId',
      setLogOut: 'setLogOut',
      setWallet: 'setWallet',
      setProjectCardUserList: 'setProjectCardUserList',
      setMarkketing: 'setMarkketing',
      setAppPermission: 'setAppPermission',
      setIsVIP: 'setIsVIP',
      setIsLoginStatus: 'setIsLoginStatus'
    }),
    getList(e) {
      if (e && e.length !== 0) {
        this.hasGeneralizationMap = true
      } else {
        this.hasGeneralizationMap = false
      }
    },
    // 获取用户信息
    async getUserInfo() {
      const res = await apiQueryUserinfo()
      if (res.code === 0) {
        this.setUserInfo(res.data)
        // 设置登录状态
        this.setIsLoginStatus(true)
        console.log('res用户信息', res);
        // 订单
        this.getUserOrder(res.data.company_id, res.data.person_no, res.data.user_id)
        if (res.data.person_no) {
          this.getUserWalletList(res.data.person_no)
        }
        // 加载食谱计划列表
        if (this.$refs.menuPlan) {
          this.$refs.menuPlan.getHealthyIndexMealPlan()
        }
      } else {
        uni.$u.toast(res.msg)
      }
    },
    // 获取最近四类订单 和已预约订单，首次调用时（onLoad）与getUserInfo一起调用
    async getUserOrder(id, personNo, userId) {
      const res = await apiBookingUserOrderGetUserLastOrder({
        company_id: id,
        person_no: personNo,
        user_id: userId
      })
      if (res.code === 0) {
        // console.log('0层', res)
        if (res.data.last_order && res.data.last_order.fee) {
          this.lastOrder = res.data.last_order
          // console.log('1层', this.lastOrder);
          if (this.lastOrder.data) {
            this.lastOrder.date = dayjs(this.lastOrder.date).format('YYYY年M月DD日')
          }
          if (this.lastOrder.fee !== 0 && this.lastOrder.fee) {
            this.lastOrder.fee = NP.divide(this.lastOrder.fee, 100)
            this.isLastOrder = true
          }
        } else {
          this.lastOrder = {}
        }
        if (res.data.now_meal_order.length) {
          this.nowMealOrder = res.data.now_meal_order
        } else if (res.data.now_meal_order.length === 0) {
          this.nowMealOrder = []
        } else {
          uni.$u.toast(res.msg)
        }
      }
    },
    // 初始化中行
    async initBocapp(option) {
      // 是否登陆状态
      this.$showLoading({
        title: '加载中...',
        mask: true
      })
      await this.$sleep(500)
      uni.hideLoading()
      if (window.c_plugins && window.c_plugins.merchantBridge) {
        window.c_plugins.merchantBridge.judgeLogin(
          data => {
            // yes 登陆成功 no 是失败
            if (data.isLogin === 'yes') {
              // 获取用户信息
              window.c_plugins.merchantBridge.getBocCustomerAllInfo(
                data => {
                  let params = {
                    cipher_text: data.cipherText
                  }
                  // 调接口
                  this.getBocPhoneLogin(params)
                },
                function (err) {
                  alert(err.message || err || '网络错误，请检查网络连接')
                },
                {
                  // 传参 先写死
                  merId: '10366'
                }
              )
            } else if (data.isLogin === 'no') {
              // 如果没登陆就跳转中行登陆
              window.c_plugins.merchantBridge.continueAfterLogin(
                () => {
                  // 需再获取一遍客户信息
                  // 跳转前如点击支付，则调起支付控件；
                  // 跳转前如点击交易记录，则跳转交易记录；
                  // 跳转前如点击优惠券，则跳转优惠券
                },
                function (err) {
                  alert(err.message || err || '网络错误，请检查网络连接')
                },
                {}
              )
            }
            // “yes”登录;“no”未登录
          },
          function (err) {
            alert(err.message || err || '网络错误，请检查网络连接')
          },
          {}
        )
      } else {
        this.login(option)
      }
    },
    async initLogin(option) {
      console.log('option', option)
      // 如果存在query数据则缓存下
      // 留着备用吧
      if (option && Object.keys(option).length > 0) {
        Cache.set('INDEX_QUERY', option, 86400)
      }
      if (option.company_id) {
        this.companyId = option.company_id
        uni.setStorageSync('companyId', option.company_id)
        // 单独存储一个company /user/project_list 这个接口需要用
        Cache.set('projectListCompanyId', option.company_id, 86400)
      } else if (this.codeInfo) { //  // 扫码的时候，要根据码的公司id直接进行登录
        this.companyId = this.codeInfo.codeCompanyId
      } else {
        // 不要自动请除了，如果需要切换其它项目点请退出登录后重新选择项目点
        // Cache.remove('projectListCompanyId')
      }
      if (option.appid) {
        this.appid = option.appid
        Cache.set('appid', option.appid)
      } else {
        Cache.remove('appid')
      }
      // 邮储的返回食堂
      if (option.backUrl) {
        this.setBackUrl(option.backUrl)
      }
      // 邮储直接带bg_sessionid进来
      if (option.bg_sessionid) {
        // httpOnly不能设置
        // setCookie('bg_sessionid', option.bg_sessionid)
        this.$store.commit('SETCOOKIE', `bg_sessionid=${option.bg_sessionid}`)
      }
      // 为了演示给客户看，添加setDemoCompany方法，当前方法只在有type=demo参数下生效
      setDemoCompany(option)
      // 是bocapp 中行标识
      if (this.platform === 'bocapp') {
        // 如果是中行再调用该js
        const ua = window.navigator.userAgent.toLowerCase()
        let bocMerchant = ''
        if (ua.indexOf('iphone') > -1) {
          bocMerchant = 'https://ebsnew.boc.cn/bocphone/BocMerchant/paylib/ios.js'
        } else if (ua.indexOf('android') > -1 || ua.indexOf('linux') > -1) {
          bocMerchant = 'https://ebsnew.boc.cn/bocphone/BocMerchant/paylib/android.js'
        }
        loadSyncFile('script', bocMerchant, 'text/javascript').then(() => {
          this.initBocapp(option)
        })
      } else {
        // if (process.env.NODE_ENV == "development") {
        // this.tempLogin();
        // } else {
        this.login(option)
        // }
      }
    },
    // 本地登陆 H5
    async tempLogin() {
      let params = {
        appid: this.appid,
        company_id: this.companyId ? this.companyId : uni.getStorageSync('companyId')
      }
      apiBookingLoginH5LoginTemp(params)
        .then(res => {
          if (res.code == 0) {
            if (!res.data.phone) {
              this.setLogOut()
              // #ifdef H5
              this.$miRouter.replace({
                path: '/pages/login/login'
              })
              // #endif
              // #ifdef MP-WEIXIN
              this.$miRouter.replace({
                path: '/pages/login/wx_login'
              })
              // #endif
              return
            }
            this.setUserInfo(res.data)
            // this.setCookie(true)
            // this.userInfo = res.data
            if (res.data.company_id) {
              this.isBindProject = true
              //登录成功后，获取下权限
              this.getPermissionList(res.data.company_id)
            } else {
              this.isBindProject = false
            }
            // this.getWechatCongfigGet(res.data.appid)
            // h5 本地测试 别删
            // this.setLoginUserId(res.data.user_id)
            // if(!res.data.company.length){
            // 	this.$miRouter.push({
            // 		path: '/pages_info/userinfo/bind_items'
            // 	})
            // }
            // 健康文章
            // this.getArticleList()
          } else {
            uni.$u.toast(res.msg)
          }
        })
        .catch(error => {
          uni.$u.toast(error.message)
        })
    },
    // 授权登录
    login(options) {
      this.$showLoading({
        title: '登录中',
        mask: true
      })
      let params = {}
      if (options.ua) {
        params.ua = options.ua
      }
      if (this.companyId || uni.getStorageSync('companyId')) {
        params.company_id = this.companyId ? this.companyId : uni.getStorageSync('companyId')
      }
      if (this.appid) {
        params.appid = this.appid
      }
      apiBookingLoginH5Login(params)
        .then(async res => {
          uni.hideLoading()
          // 调用方法设置初始化数据
          this.initLoginSetInfo(res)
        })
        .catch(err => {
          uni.hideLoading()
          uni.$u.toast(err.message)
        })
    },
    // 因为中行也需要登陆返回的信息
    async initLoginSetInfo(res) {
      if (res.code == 0) {
        if (!res.data.phone) {
          this.clearIntervalFun()
          this.setLogOut()
          setTimeout(() => {
            // #ifdef H5
            this.$miRouter.replace({
              path: '/pages/login/login'
            })
            // #endif
            // #ifdef MP-WEIXIN
            this.$miRouter.replace({
              path: '/pages/login/wx_login'
            })
            // #endif
          }, 500)
          return
        }
        // 如果当前是农行，并且当前设置了弹出农行需要得弹窗
        let isShowPopup = res.data ? res.data.is_abc_statement : false
        if (this.platform === 'abc' && isShowPopup) {
          this.showFirstTipDialog = true
        }
        // 最近四类订单数据 + 钱包总余额
        this.getUserInfo()
        this.getAgreementList(res.data)
        getFeedbackReadState()
        //       if (this.userInfo.phone && this.userInfo.company_id) {
        //         await this.getSignList()
        //       }
        await this.getMarketingSettingHandle()
        // this.userInfo = res.data
        if (res.data.company_id) {
          this.isBindProject = true
          //登录成功后，获取下权限
          this.getPermissionList(res.data.company_id)
          if (this.userInfo?.person_no) {
            this.getUserSubsidyList() // 获取补贴
          }
          // this.GetBookingHealthyDietReport()
          res.data.company_id && this.getQuestionList()
        } else {
          this.isBindProject = false
          // 自注册审批
          this.getAutoRegisterList()
        }
        // if (!res.data.person_no) {
        //   let params = {
        //     company_id: res.data.company_id,
        //     user_id: res.data.user_id
        //   }
        //   // 获取该手机下的列表
        //   this.getBindProjectCardUserList(params)
        // }
        // #ifdef H5
        // var ua = window.navigator.userAgent.toLowerCase()
        // if (ua.match(/MicroMessenger/i) == 'micromessenger') {
          // this.getWechatCongfigGet(res.data.appid)
        // }
        // 设置登录状态
        // this.setIsLoginStatus(true)
        // #endif
        if (this.codeInfo) {
          // 扫码进来没登陆，跳去登录回来，如果有缓存就跳去点餐
          this.$miRouter.push({
            path: '/pages_bundle/select/select_diner'
          })
        }
        if (uni.getStorageSync('applyVisitorInfo')) {
          // 切换项目点，过来重新授权，然后再回去申请访客餐
          this.$miRouter.push({
            path: '/pages_common/meal_apply/apply_list'
          })
        }
        // this.setLoginUserId(res.data.user_id)
        // if(!res.data.company.length){
        // 	this.$miRouter.push({
        // 		path: '/pages_info/userinfo/bind_items'
        // 	})
        // }
        // if (res.data.h5_context_display) {
        //   // 健康文章
        //   this.getArticleList()
        // }
        // 营养数据 // 可能需要做一个轮循
        this.initNutrient()

        // this.flag = true
      } else if (res.code == 302) {
        // #ifdef H5
        this.$store.commit('SETCOOKIE', '')
        window.location.href = res.msg
        // #endif
        // #ifdef MP-WEIXIN
        // 小程序直接清空上一次的数据重新登录
        this.$miRouter.push({
          path: '/pages/login/login'
        })
        Cache.remove('userInfo')
        Cache.remove('membershipRights')
        Cache.remove('isVIP')
        Cache.remove('cookie')
        // #endif
      } else {
        uni.$u.toast(res.msg)
        this.clearStorage()
      }
    },
    initNutrient() {
      this.getIndexNutrient()
      // if (this.timer) return
      // 因为支付宝首次不执行onshow，然后是this.$miRouter.replaceAll回来就会重新加载生命周期所以先判断一下支付宝，看看后续有什么办法解决
      // #ifdef MP-ALIPAY
      // #endif
      // #ifdef MP-WEIXIN || H5
      // 营养数据
      // 不要用定时器哦，后端顶不住哦
      // this.timer = setInterval(() => {
      //   this.getIndexNutrient()
      // }, 3500)
      // #endif
    },
    changeSwiper(e) {
      this.currentSwiper = e.detail.current
    },
    // 清空保存的信息
    clearStorage() {
      uni.removeStorageSync('appid')
      uni.removeStorageSync('companyId')
    },
    // 设置项目点
    setUserProjectList(parmas) {
      this.$showLoading({
        title: '加载中....',
        mask: true
      })
      setUserBindProjectPoint(parmas)
        .then(res => {
          if (res.code == 0) {
            uni.$u.toast('成功')
            uni.hideLoading()
            // this.$miRouter.push({
            //   path: '/pages_info/userinfo/perfect_userinfo',
            //   query: {
            //     company_id: parmas.company_id,
            //     // user_id: parmas.user_id,
            //     person_no: parmas.person_no
            //   }
            // })
          } else {
            uni.hideLoading()
            uni.$u.toast(res.msg)
          }
        })
        .catch(err => {
          uni.$u.toast(err.message)
        })
    },
    // 获取已录入相同手机号的项目点用户列表
    getBindProjectCardUserList(parmas) {
      this.$showLoading({
        title: '获取项目点....',
        mask: true
      })
      getApiBindProjectCardUserList(parmas)
        .then(res => {
          if (res.code == 0) {
            uni.hideLoading()
            if (!res.data.length) {
              // 没有项目点直接进入绑定页面
              this.$miRouter.push('/pages_info/userinfo/bind_items')
            } else if (res.data && res.data.length == 1) {
              // 只有一个项目点 直接绑定 进入标签选择页面
              // 还有调一个绑定接口
              let formData = {
                company_id: res.data[0].company_id,
                person_no: res.data[0].person_no,
                name: res.data[0].name
              }
              this.setUserProjectList(formData)
            } else {
              // 多个项目点 进入选择项目点页面
              this.setProjectCardUserList(res.data)
              this.$miRouter.push('/pages_info/userinfo/choice_items')
            }
          } else {
            uni.hideLoading()
            uni.$u.toast(res.msg)
          }
        })
        .catch(err => {
          uni.$u.toast(err.message)
        })
    },
    // jsskd 用于调起微信扫一扫
    getWechatCongfigGet(appid) {
      let params = {
        appid: appid,
        company_id: this.userInfo.company_id,
        url: window.location.href.split('#')[0]
      }
      getApiWechatCongfigGet(params)
        .then(res => {
          uni.hideLoading()
          if (res.code == 0) {
            if (res.data && res.data.appid) {
              jweixin.config({
                debug: false,
                appId: res.data.appid,
                timestamp: res.data.timestamp,
                nonceStr: res.data.noncestr,
                signature: res.data.signature,
                jsApiList: ['checkJsApi', 'scanQRCode', 'chooseWXPay'] // 把支付也初始化
              })
            }
          } else {
            uni.$u.toast(res.msg)
          }
        })
        .catch(err => {
          uni.hideLoading()
          uni.$u.toast(err.message)
        })
    },
    // 绑盘
    getH5BingTray(params) {
      this.$showLoading({
        title: '绑定中...',
        mask: true
      })
      let paramsData = {
        org_no: this.radioOrgValue,
        ...params
      }
      getApiH5BingTray(paramsData)
        .then(res => {
          if (res.code == 0) {
            uni.hideLoading()
            this.buffetShow = false
            this.$miRouter.push({
              path: '/pages_order/order/order_lists?mode=items'
            })
          } else {
            uni.hideLoading()
            uni.$u.toast(res.msg)
          }
        })
        .catch(err => {
          uni.hideLoading()
          uni.$u.toast(err.message)
        })
    },
    // 绑盘获取消费点
    getTrayOrg(params) {
      this.$showLoading({
        title: '获取消费点...',
        mask: true
      })
      getApiTrayOrg(params)
        .then(res => {
          uni.hideLoading()
          if (res.code == 0) {
            if (res.data.length < 1) {
              this.radioOrgValue = res.data[0].id
              this.getH5BingTray(params)
            } else {
              this.buffetShow = true
              this.trayOrgList = res.data
              if(this.trayOrgList && Array.isArray(this.trayOrgList) && this.trayOrgList.length > 0 ) {
                this.radioOrgValue = this.trayOrgList[0].id || ''
              }
            }
          } else {
            uni.$u.toast(res.msg)
          }
        })
        .catch(err => {
          uni.hideLoading()
          uni.$u.toast(err.message)
        })
    },
    // 确定消费点
    clickTrayOrg() {
      if (!this.radioOrgValue) {
        return uni.$u.toast('请选择消费点')
      }
      this.getH5BingTray(this.buffetQrcodeUrlData)
    },
    // 获取用户信息
    // queryUserinfo() {
    // 	apiQueryUserinfo()
    // 		.then(res => {
    // 			this.setUserInfo(res.data)
    // 			this.userInfo = res.data
    // 		})
    // 		.catch(err => {
    // 			console.log('获取用户信息err', err)
    // 		})
    // },
    // 获取食堂
    getBookingUserGetCardUserList() {
      this.$showLoading({
        title: '获取中....',
        mask: true
      })
      getApiBookingUserGetCardUserList({
        company_id: this.userInfo.company_id
      })
        .then(res => {
          uni.hideLoading()
          if (res.code == 0) {
            if (res.data.length && res.data.length <= 1) {
              this.getBookingUserGetCanteenList(res.data[0])
            } else {
              this.$miRouter.push({
                path: '/pages_bundle/select/select_diner?type=reservation'
              })
            }
          } else {
            uni.$u.toast(res.msg)
          }
        })
        .catch(error => {
          uni.hideLoading()
          uni.$u.toast(error.message)
        })
    },
    // 获取食堂组织
    getBookingUserGetCanteenList(personData) {
      getApiBookingUserGetCanteenList({
        groups: personData.groups,
        company_id: this.userInfo.company_id,
        user_id: this.userInfo.user_id
      })
        .then(res => {
          if (res.code == 0) {
            if (res.data.length && res.data.length <= 1) {
              this.getTakeMealTypeList(personData, res.data[0])
            } else {
              this.$miRouter.push({
                path: '/pages_bundle/select/select_diner?type=reservation'
              })
            }
          } else {
            uni.$u.toast(res.msg)
          }
        })
        .catch(error => {
          uni.$u.toast(error.message)
        })
    },
    // 获取堂食方式
    getTakeMealTypeList(personData, org) {
      getApiTakeMealTypeList({
        groups: personData.groups,
        organization_id: org.org_id
      })
        .then(res => {
          if (res.code == 0) {
            if (res.data.length && res.data.length <= 1 && res.data[0].take_meal !== 'waimai') {
              this.SET_SELECT({
                key: 'person',
                data: personData
              })
              this.SET_SELECT({
                key: 'org',
                data: org
              })
              this.SET_SELECT({
                key: 'take_meal_type',
                data: res.data[0].take_meal
              })
              this.$miRouter.push({
                path: '/pages_bundle/appoint/appoint_order'
              })
            } else {
              this.$miRouter.push({
                path: '/pages_bundle/select/select_diner?type=reservation'
              })
            }
          } else {
            uni.$u.toast(res.msg)
          }
        })
        .catch(error => {
          uni.$u.toast(error.message)
        })
    },
    async handleClickMenu(item) {
      let _this = this
      // #ifdef H5
      // 友盟埋点
      if (aplus_queue) {
        aplus_queue.push({
          action: 'aplus.record',
          arguments: [
            'eventCode',
            'CLK',
            {
              handle: 'handleClickMenu'
            }
          ]
        })
      }
      // #endif
      if (item.type == 'bind_buffet') {
        this.$nextTick(function () {
          // #ifdef H5
          if (this.platform === 'wechat') {
            // 微信环境
            jweixin.ready(() => {
              jweixin.scanQRCode({
                // desc: 'scanQRCode desc',
                needResult: 1, // 默认为0，扫描结果由微信处理，1则直接返回扫描结果，
                scanType: ['qrCode', 'barCode'], // 可以指定扫二维码还是一维码，默认二者都有
                success: function (res) {
                  // var result = res.resultStr; // 当needResult 为 1 时，扫码返回的结果
                  _this.buffetQrcodeUrlData = _this.getparamRrCode(res.resultStr)
                  _this.getTrayOrg(_this.buffetQrcodeUrlData)
                },
                error: function (res) {
                  if (res.errMsg.indexOf('function_not_exist') > 0) {
                    alert('版本过低请升级')
                  }
                }
              })
            })
          } else if (this.platform === 'mp-alipay') {
            // 支付宝
            function ready(callback) {
              // 如果jsbridge已经注入则直接调用
              if (window.AlipayJSBridge) {
                callback && callback()
              } else {
                // 如果没有注入则监听注入的事件
                document.addEventListener('AlipayJSBridgeReady', callback, false)
              }
            }
            ready(function () {
              AlipayJSBridge.call(
                'scan',
                {
                  scanType: ['qrCode', 'barCode']
                },
                function (result) {
                  // alert(JSON.stringify(result));
                  _this.buffetQrcodeUrlData = _this.getparamRrCode(result.codeContent)
                  _this.getTrayOrg(_this.buffetQrcodeUrlData)
                }
              )
            })
          }
          // #endif
          // #ifdef MP-WEIXIN || MP-ALIPAY
          uni.scanCode({
            onlyFromCamera: true,
            scanType: ['qrCode', 'barCode'],
            success: function (res) {
              _this.buffetQrcodeUrlData = _this.getparamRrCode(res.result)
              _this.getTrayOrg(_this.buffetQrcodeUrlData)
            },
            fail: function (err) {
              console.log('error', err)
            }
          })
          // #endif
        })
      } else if (item.type == 'recharge') {
        this.getRechargeWalletList()
      } else if (item.type == 'reservation') {
        this.SET_SELECT({
          key: 'payment_order_type',
          data: item.type
        })
         // this.getBookingUserGetCardUserList()
         this.getBookingUserGetUserOrderSetting()
      } else if (item.type == 'intention_menu') {
				// 因为要跳到选择组织页面
				if (this.$store.state.appoint) {
					const state = this.$store.state.appoint
					if (state.select.intent_org_id && state.select.intent_org_id.id) {
						this.$miRouter.push(item.path)
					} else {
						this.$miRouter.push(item.orgPath)
					}
				} else {
						this.$miRouter.push(item.orgPath)
				}
			} else if (item.type == "zk_laundry") {
        // 洗衣系统对接跳转，分微信与H5环境
        this.goToWashingSystem()
			} else if (item.type == 'ebank') {
				this.gotoCheckEaccount()
			} else if (item.type == 'facepass_cb') {
        // let res = await apiAlipayGetFacepassStatusPost()
        //   if (res.code === 0) {
        //     if (res.data && res.data.status === 'sign') {
              this.$miRouter.push({
                path: item.path,
                query: {
                  person_no: this.userInfo.person_no // 兼容多用户
                }
              })
          //   } else {
          //     uni.$u.toast('请开通一脸通行否则无法享受支付宝餐补')
          //   }
          // }
      } else if (item.type === 'third_shop') {
        this.getThirdShow()
      } else if (item.type === 'mei_tuan') {
        this.getThirdMeituan()
      } else if (item.type === 'zhxy') {
        console.log('点了智慧校园')
        const [err, res] = await this.$to(apiBookingYideAuthUrl({}))
        if (err) {
          return uni.$u.toast(err.msg)
        }
        if (res.code === 0) {
          console.log('res.data', res.data)
          if (res.data?.url) {
            window.location.href = res.data.url
          } else {
            uni.$u.toast('跳转失败')
          }
        } else {
          uni.$u.toast(res.msg)
        }
      } else {
        if (item.path) {
          if (item.type == 'report_meal') {
            this.SET_SELECT({
              key: 'payment_order_type',
              data: item.type
            })
          }
          // 因为有了同一个字段
          if (item.type == 'myReservation') {
            this.SET_SELECT({
              key: 'payment_order_type',
              data: 'reservation'
            })
          }
          if (item.type == 'shop_feedback') {
            this.$miRouter.push({
              path: item.path,
              query: {
                type: 'shop',
                person_no: this.userInfo.person_no // 兼容多用户
              }
            })
            return
          }
          if (item.type == 'coupon') {
            this.getApiGetCouponManageReceiveList()
            return
          }
          this.$miRouter.push({
            path: item.path,
            query: {
              person_no: this.userInfo.person_no // 兼容多用户
            }
          })
        } else {
          this.$toast({
            title: '请期待'
          })
        }
      }
    },

    // 拿到参数切成对象  传参"name=张三&sex=1&address=中国";
    //{name: "张三", sex: "1", address: "中国"}
    getparamRrCode(data) {
      let arr = data.split('&') //["name=张三", "sex=1", "address=中国"]
      let obj = {}
      for (let i = 0; i < arr.length; i++) {
        let arr2 = arr[i].split('=') //["name", "张三"]["sex", "1"]["address", "中国"]
        obj[arr2[0]] = arr2[1]
      }
      return obj
    },
    // 获取充值钱包列表
    getRechargeWalletList() {
      this.$showLoading({
        title: '获取中....',
        mask: true
      })
      getApiRechargeWalletList()
        .then(res => {
          uni.hideLoading()
          if (res.code == 0) {
            // 如果该手机账号只有一个项目点，且只有一个组织的储值钱包时，不需要选择钱包，直接进入充值页面
            if (res.data && res.data.length == 1 && res.data[0].wallet_list && res.data[0].wallet_list.length == 1) {
              this.walletData = res.data[0].wallet_list[0]
              this.getRechargeGetSettings()
              // this.setWallet(res.data[0].wallet_list[0])
              // this.$miRouter.push({
              //   path: '/pages_bundle/recharge/recharge',
              //   query: {
              //     person_no: this.userInfo.person_no // 兼容多用户
              //   }
              // })
            } else {
              if (res.data && res.data.length) {
                this.$miRouter.push({
                  path: '/pages_bundle/recharge/select_wallet',
                  query: {
                    routeType: 'index',
                    person_no: this.userInfo.person_no // 兼容多用户
                  }
                })
              } else {
                uni.$u.toast('暂无充值钱包')
              }
            }
            // /pages_bundle/recharge/recharge
            // /pages_bundle/recharge/select_wallet
          } else {
            uni.$u.toast(res.msg)
          }
        })
        .catch(err => {
          uni.hideLoading()
          uni.$u.toast(err.message)
        })
    },
    // 校验一下后台是否配置去农行缴费
    getRechargeGetSettings() {
      this.$showLoading({
        title: '获取中....',
        mask: true
      })
      let params = {
        org_id: this.walletData.org_id,
        person_no: this.userInfo.person_no,
      }
      if (this.walletData.wallet_id) {
        params.wallet_id = this.walletData.wallet_id
      }
      getApiRechargeGetSettingsV2(params)
        .then(res => {
          uni.hideLoading()
          if (res.code == 0) { // 走正常流程
            this.setWallet(this.walletData)
            this.$miRouter.push({
              path: '/pages_bundle/recharge/recharge',
              query: {
                person_no: this.userInfo.person_no // 兼容多用户
              }
            })
          } else if (res.code == 302) { // 302就去农行
            // #ifdef H5
            window.location.href = res.msg
            // #endif
          } else {
            uni.$u.toast(res.msg)
          }
        })
        .catch(err => {
          uni.hideLoading()
          uni.$u.toast(err)
        })
    },
    // 获取饮食报告
    // GetBookingHealthyDietReport() {
    //   this.$showLoading({
    //     title: '获取中....',
    //     mask: true
    //   })
    //   let params = { company_id: String(this.userInfo.company_id) }
    //   apiGetBookingHealthyDietReport(params)
    //     .then(res => {
    //       uni.hideLoading()
    //       if (res.code === 0) {
    //         this.segmentNot = true
    //         if (Object.keys(res.data).length !== 0) {
    //           //有饮食报告数据
    //           this.isShowReport = true
    //           this.reportDetails = {
    //             energyKcal: res.data.energy_kcal,
    //             createTime: res.data.create_time,
    //             mealTypeAlias: res.data.meal_type_alias,
    //             imgNum: res.data.food_scene_images.length,
    //             id: res.data.id,
    //             foodSceneImages:
    //               res.data.food_scene_images.length > 0 ? res.data.food_scene_images[0] : '../../static/images/default.png'
    //           }
    //         } else {
    //           //无饮食报告数据
    //           this.isShowReport = false
    //         }
    //       } else {
    //         this.segmentNot = false
    //         uni.$u.toast(res.msg)
    //       }
    //     })
    //     .catch(err => {
    //       uni.hideLoading()
    //       uni.$u.toast(err.message)
    //     })
    // },
    healthyEvaluationPath() {
      if (this.userInfo.h5_context_display) {
        this.$miRouter.push('/pages_health/healthy/assessment/healthy_assessment_entrance')
      } else {
        this.$miRouter.push('/pages_health/healthy/diet_guide/diet_guide')
      }
    },
    // bobyManagementPath() {
    //   this.$miRouter.push('/pages_health/healthy/body_management/situation')
    // },
    dietaryManagementPath() {
      if (this.userInfo.healthy_info) {
        this.$miRouter.push('/pages_health/healthy/menu/menu_recommend')
      } else {
        Cache.remove('PERFECT_USERINFO')
        this.$miRouter.push({
          path: '/pages_info/userinfo/perfect_userinfo',
          query: {
            company_id: this.userInfo.company_id
          }
        })
        // 膳食营养页面
        // this.$miRouter.push('/pages_health/healthy/diet_guide/diet_guide')
      }
    },
    // 营养
    getIndexNutrient() {
      getApiIndexNutrient()
        .then(res => {
          uni.hideLoading()
          if (res.code == 0) {
            this.indexNutrientData = res.data
            let indexEnergyKcal =
              res.data.index_energy_kcal / res.data.need_energy_kcal ? res.data.index_energy_kcal / res.data.need_energy_kcal : 0
            let useSportsEnergyKcal =
              res.data.use_sports_energy_kcal / res.data.need_sports_energy_kcal
                ? res.data.use_sports_energy_kcal / res.data.need_sports_energy_kcal
                : 0
            let canEnergyKcal =
              res.data.can_energy_kcal / res.data.need_energy_kcal ? res.data.can_energy_kcal / res.data.need_energy_kcal : 0

            this.indexNutrientData.index_energy_kcal_percent =
              indexEnergyKcal * 100 >= 100 ? 100 : Number((indexEnergyKcal * 100).toFixed(2))
            this.indexNutrientData.use_sports_energy_kcal_percent =
              useSportsEnergyKcal * 100 >= 100 ? 100 : Number((useSportsEnergyKcal * 100).toFixed(2))
            this.indexNutrientData.can_energy_kcal_percent =
              canEnergyKcal * 100 >= 100 ? 100 : Number((canEnergyKcal * 100).toFixed(2))
          } else {
            uni.$u.toast(res.msg)
          }
        })
        .catch(err => {
          this.clearIntervalFun()
          // uni.$u.toast(err.message)
        })
    },
    // 格式化文字 超过多少显示...
    nameFormat(name, number) {
      if (!name) return
      let subStr = name.slice(0, number)
      subStr = subStr + (name.length > number ? '...' : '')
      return subStr
    },
    // 点击消除能量球
    getClearEnergyEgg(params, index) {
      // this.indexNutrientData.energy_egg.splice(index,1)
      getApiClearEnergyEgg({
        energy_kcal: params.energy_kcal,
        meal_type: params.meal_type,
        payment_food_id: params.payment_food_id
      })
        .then(res => {
          uni.hideLoading()
          if (res.code == 0) {
            this.getIndexNutrient()
          } else {
            uni.$u.toast(res.msg)
          }
        })
        .catch(err => {
          uni.$u.toast(err.message)
        })
    },
    // 健康文章
    getArticleList() {
      this.$showLoading({
        title: '获取咨询文章...',
        mask: true
      })
      getApiArticleList({
        page: 1,
        page_size: 5,
        is_recommend: true
      })
        .then(res => {
          uni.hideLoading()
          if (res.code == 0) {
            this.articleList = res.data.results
          } else {
            uni.$u.toast(res.msg)
          }
        })
        .catch(err => {
          uni.hideLoading()
          uni.$u.toast(err.message)
        })
    },
    gotoPathDiscovery() {
      this.$miRouter.push('/pages_health/article/article')
    },
    clickArticlePath(item) {
      this.$miRouter.push({
        path: '/pages_health/article/article_details',
        query: {
          id: item.id
        }
      })
    },
    pathpHoto() {
      this.$miRouter.push({
        path: '/pages/photo_recognition/photo_recognition'
      })
    },
    // 清除倒计时
    clearIntervalFun() {
      if (this.timer) {
        clearInterval(this.timer)
        this.timer = null
      }
    },
    // 获取功能配置，也就是权限啦
    getPermissionList(company_id) {
      this.$showLoading({
        title: '获取中....',
        mask: true
      })
      getApiPermission({
        id: company_id
      })
        .then(res => {
          uni.hideLoading()
          if (res.code == 0) {
            // 存在vuex
            this.setAppPermission(res.data.already_permission)
            this.setIsVIP(res.data.is_member_on)
            this.isGetGeneralizationMapShow = Cache.get('isVIP')
            this.refresh ++
            this.menuList = []
            let filterMenuList = []
            // 先处理权限的，要根据后台返回的权限进行排序
            res.data.already_permission.map(item => {
              this.allMenuList.map(menuItem => {
                if (item === menuItem.permission) {
                  filterMenuList.push(menuItem)
                }
              })
                // 如果有视频公话权限 才调这个接口，需要拿到未读信息
                if (item === 'voip') {
                  this.getUserVoipInfo()
                }
            })
            // 再把前端这边设置no_permission的push上去,用于还没有配置权限的功能
            let noPermissionList = this.allMenuList.filter(item => item.no_permission)
            filterMenuList.push(...noPermissionList)
            Cache.set('menuList', filterMenuList)
            console.log(23232, filterMenuList)
            this.permissionMenuList = filterMenuList
            // 数据分组
            // filterMenuList.map((item, i) => {
            //   let k = Math.floor(i / 10) // index
            //   if (!this.menuList[k]) this.menuList[k] = []
            //   this.menuList[k].push(item)
            // })
          } else {
            uni.$u.toast(res.msg)
          }
        })
        .catch(err => {
          uni.hideLoading()
          uni.$u.toast(err.message)
        })
    },
    async getMarketingSettingHandle() {
      this.$showLoading({
        title: '获取设置中...',
        mask: true
      })
      const [err, res] = await this.$to(getMarketingSettings())
      uni.hideLoading()
      if (err) {
        return
      }
      if (res.code === 0) {
        this.isShowPopup = true
        this.setMarkketing(res.data)
        // 请求完之后再显示浮窗
        console.log('页面显示', this.floatingPopupShow)
        this.floatingPopupShow = !this.floatingPopupShow
      } else {
        uni.$u.toast(res.msg)
      }
    },
    // 中行登陆接口
    async getBocPhoneLogin(params) {
      this.$showLoading({
        title: '登录中...',
        mask: true
      })
      const [err, res] = await this.$to(getApiBocPhoneLogin(params))
      uni.hideLoading()
      if (err) {
        return
      }
      // 调用方法设置初始化数据1
      this.initLoginSetInfo(res)
    },
    // 点击banner
    clickBanner(index) {
      let current = this.banner[index]
      // 没有jump_url则为不跳转
      if (!current.jump_url) return
      if (current.jump_type === 'inner') {
        // 内部地址
        for (let i = 0; i < this.menuList.length; i++) {
          const list = this.menuList[i]
          for (let k = 0; k < list.length; k++) {
            const menu = list[k]
            if (menu.permission === current.jump_url) {
              // this.$miRouter.push(menu.path)
              this.handleClickMenu(menu)
              break
            }
          }
        }
      } else {
        // 外部链接
        // 是bocapp 中行标识
        if (this.platform === 'bocapp') {
          if (current.jump_url.includes('BOCBANK')) {
            window.c_plugins.merchantBridge.goToNative(
              function () {},
              function (err) {},
              {
                page: current.jump_url
              } // 包括BOCBANK:协议类型；xxxx中行加密串类型；传0则返回app首页
            )
          }
        } else {
          // 小程序用外部链接要配H5域名(这里先不做小程序的外部链接)
          // #ifdef H5
          window.location.href = current.jump_url
          // #endif
        }
      }
    },
    // 邮储设置backUrl
    setBackUrl(url) {
      // url = 'http%3A%2F%2F183.162.246.181%3A10002%2Fdev%2Fstatic%2Fback%2Fjump.html%3FpageId%3D50000000%26h5Type%3D9079%26fromId%3Dinclude_sb%26toRouter%3Db2c'
      // 切换服务商逻辑：backUrl1.把h5Type改成9041 2.拼接switchService=true
      // 先处理好数据再保存吧，其它地方估计也会用到
      let backUrl = ''
      let options = {}
      url = decodeURIComponent(url)
      // 原url
      Cache.set('originBackUrl', url, 86400)
      const urlArr = url.split('?')
      options.origin = urlArr[0]
      let query = getQueryObject(url)
      options.query = query ? query : {}
      options.query.h5Type = 9041
      options.query.switchService = true
      backUrl = options.origin + objectToQuery(options.query)
      Cache.set('backUrl', backUrl, 86400) // 存下本地，默认缓存1天
    },
    // 关闭页面
    closePageHandle() {
      // window.close()
      // location.reload();
      let backurl = Cache.get('backUrl')
      if (backurl) {
        location.href = backurl
      }
    },
    exitApp,
    closeAllAPP,
    // 获取未领取补贴
    getUserSubsidyList() {
      apiGetUserUnreceivedSubsidy({
        person_no: this.userInfo.person_no,
        company_id: this.userInfo.company_id
      })
        .then(res => {
          if (res.code == 0) {
            if (res.data.length) {
              this.showReceiveSubsidy = true
            } else {
              this.showReceiveSubsidy = false
            }
          } else {
            uni.$u.toast(res.msg)
          }
        })
        .catch(err => {
          console.log(err)
        })
    },
    // 获取更新的协议
    async getAgreementList(info) {
      const [err, res] = await this.$to(
        checkAgreementApi({
          user_id: info ? info.user_id : this.userInfo.user_id
        })
      )
      if (err) {
        return
      }
      if (res.code === 0) {
        this.updateAgreementFaceList = []
        this.updateAgreementList = []
        this.popupLayoutConfirmStyle = {
          backgroundColor: this.variables.colorPrimary,
          color: '#fff',
          width: '45%',
          height: '65rpx',
          border: `1px solid ${this.variables.colorPrimary}`
        }
        this.popupLayoutCancelStyle = {
          width: '45%',
          height: '65rpx',
          color: this.variables.colorPrimary, // '#52da98',
          border: `1px solid ${this.variables.colorPrimary}`
        }
        res.data.agreements.forEach(v => {
          v.agreement_type_alias = v.agreement_type_alias.split('-')[0]
          // 更新的刷脸协议单独拿出来列表
          if (v.agreement_type === 'FPSA') {
            this.updateAgreementFaceList.push(v)
          }else {
            this.updateAgreementList.push(v)
          }
        })
        if (this.updateAgreementList.length > 0) {
          // Cache.set('AGREEMENTLIST', JSON.stringify(this.updateAgreementList))
          this.showUpdateAgreement = true
        } else if (this.updateAgreementFaceList.length > 0) {
          this.showUpdateAgreementFace = true
        }
      }
    },
    clickAgreementBtn(e) {
      if (e == 'confirm') {
        this.updateFaceAgreement(this.userInfo)
        // Cache.set('AGREEMENTLIST', JSON.stringify(['face']))
      } else {
        this.clearIntervalFun()
        this.setLogOut()
        this.$miRouter.replace({
          path: '/pages/login/login'
        })
      }
    },
    async clickAgreementBtnFace(e) {
      if (e == 'confirm') {
        // 是刷脸点协议的时候
        this.updateFaceAgreement(this.userInfo, 'face')
      } else {
        this.clearIntervalFun()
        // 取消关闭人脸开关
        await this.updateSwitchFacepay()
        this.showUpdateAgreementFace = false
        console.log("clickAgreementBtnFace", e)
        // this.$miRouter.push({
        //   path: '/pages_info/face_gather/face_gather'
        // })
      }
    },
    // 更新协议
    async updateFaceAgreement(info, type) {
      let params = {
        agreement_types: []
      }
      if (info.user_id) params.user_id = info.user_id
      // 刷脸单独处理
      if (type === 'face') {
        this.updateAgreementFaceList.forEach(v => {
          params.agreement_types.push(v.agreement_type)
        })
      }else {
        this.updateAgreementList.forEach(v => {
          params.agreement_types.push(v.agreement_type)
        })
      }
      if (params.agreement_types.length) {
        await updateAgreementApi(params)
        if (type === 'face') {
          this.showUpdateAgreementFace = false
        }else {
          if (this.updateAgreementFaceList.length > 0) {
            this.showUpdateAgreementFace = true
          }
          this.showUpdateAgreement = false
        }
      }
    },
    gotoAgreementDetail(row) {
      this.$miRouter.push({
        path: '/pages_info/agreement/detail',
        query: {
          type: row.agreement_type,
          key: 'AGREEMENTLIST',
          title: row.agreement_type_alias
        }
      })
    },
    //点击饮食数据
    async jumpDietary(id) {
      // console.log('点击了饮食报告')
      // let hasPermissions = await this.checkMemberPermissions('diet_report', true)
      // if (hasPermissions) {
      //   this.$miRouter.push({
      //     path: '/pages_health_pomr/diet_report/index',
      //     query: {
      //       id: id
      //     }
      //   })
      // }
      this.$miRouter.push({
          path: '/pages_health_pomr/diet_report/index',
          query: {
            id: id
          }
        })
    },
    //点击手动记录
    manualSelection() {
      //判断是否完善健康档案
      // if (this.healthyInfo) {
      //   // console.log(1);
      //   if (this.notVip) {
      //     this.notShow = false
      //是会员跳转报告列表页面
      this.$miRouter.push({
        path: '/pages_health/record_food/manual_record'
      })
      //   } else {
      //     this.notShow = true
      //   }
      // } else {
      //   uni.$u.toast('请先完善健康档案')
      // }
    },
    //点击更多报告
    moreReports() {
      // //判断是否完善健康档案
      // if (this.healthyInfo) {
      //   // console.log(1);
      //   if (this.notVip) {
      //     this.notShow = false
      //     //是会员跳转报告列表页面
      this.$miRouter.push({
        path: '/pages_health_pomr/reports_list/index'
      })
      //   } else {
      //     this.notShow = true
      //   }
      // } else {
      //   uni.$u.toast('请先完善健康档案')
      // }
    },
    onPageScroll(e) {
      const top = uni.upx2px(100)
      const { scrollTop } = e
      let percent = scrollTop / top > 1 ? 1 : scrollTop / top
      this.navBg = percent
    },
    // 检查开户情况
    async gotoCheckEaccount() {
      this.$showLoading({
        title: '请求中...',
        mask: true
      })
      let res = await apiCheckEaccountOpen({
        company_id: this.userInfo.company_id
      })
      uni.hideLoading()
      // 如果有开户就跳转去农行转入转出页面，没有就返回code=2
      if (res.code === 0) {
        window.location.href = res.data.redirect
      } else if (res.code === 2) {
        this.$miRouter.push({
          path: '/pages_info/user_config/abc_account/abc_un_eaccount'
        })
      } else {
        uni.$u.toast(res.msg)
      }
    },
    //  获取农行的配置
    async getOrgBuryBankSetting() {
      var params = {
        company_id: this.userInfo.company_id
      }
      const [err, res] = await this.$to(apiBookingAbcGetOrgBuryInfo(params))
      if (err) { return }
      if (res && res.code === 0) {
        var data = res.data || {}
        if (data && Reflect.has(data, 'bury_name')) {
          var name = data.bury_name || ''
          let fireflyProdCode = data.firefly_prod_code || ''
          let fireflyProdName = data.firefly_prod_name || ''
          let fireflyProdType = data.firefly_prod_type || ''
          let pageProdInfo1 = data.page_prod_info_1 || ''
          console.log("name", name, fireflyProdCode, fireflyProdName, fireflyProdType, pageProdInfo1);
          if (name) {
            initSensors(name)
            const sensors = window.sensors || ''
            if (sensors) {
              console.log("sensors 调用");
              sensors.quick("autoTrackSinglePage", { "$title": "智慧食堂",firefly_prod_name: fireflyProdName,firefly_prod_code: fireflyProdCode,firefly_prod_type: fireflyProdType, pageprodinfo_1: pageProdInfo1 });
            }
          }
        }
      }
    },
    // 获取用户钱包总余额
    async getUserWalletList(person_no) {
      const res = await getApiUserWalletList({
        person_no
      })
      if (res.code === 0 && res.data.total && res.data.total !== 0) {
        this.userWallet = NP.divide(res.data.total, 100)
      } else if (res.code === 0 && res.data.total === 0) {
        this.userWallet = 0.00.toFixed(2)
      } else {
        uni.$u.toast(res.msg)
      }
    },
    // 预约订单点击
    getOrderQrcode() {
      this.qrcode = this.nowMealOrder[this.currentIndex].verify_code
      this.qrcodePopupShow = true
    },
    changeIndex(val) {
      this.currentIndex = val.detail.current
    },
    // 现有订单二维码确定按钮
    closeQrcode() {
      this.qrcodePopupShow = false
    },
    goToWeeklyReport() {
      this.isShowWeeklyReport = false
      this.$miRouter.push({
        path: '/pages_info/news/weekly_report',
        query: {
          id: this.weeklyNewId,
          start: this.dateRange[0],
          end: this.dateRange[1]
        }
      })
    },
    // 获取预约点餐信息，三个接口合并一个
    async getBookingUserGetUserOrderSetting() {
      var params = {
        is_visitor: false,
        h5_type: 'reservation',
        company_id: this.userInfo.company_id
      }
      this.$showLoading({
        title: '加载中...',
        mask: true
      })
      const [err, res] = await this.$to(apiBookingUserGetUserOrderSetting(params))
      uni.hideLoading()
      console.log("getBookingUserGetUserOrderSetting", res);
      if (err) {
        return this.$u.toast(err.message || '获取点餐信息失败')
      }
      if (res && res.code === 0) {
        console.log("getBookingUserGetUserOrderSetting", res);
        var data = res.data || {}
        if (data && typeof data === 'object') {
          Cache.set('reservationInfo', data)
          var userListData = Reflect.has(data, 'user_list_data') ? data.user_list_data :  {}
          var canteenListData = Reflect.has(data, 'canteen_list_data') ? data.canteen_list_data : {}
          var takeMealTypeData = Reflect.has(data, 'take_meal_type_data') ? data.take_meal_type_data : []
          // 多个卡信息
          if (userListData && Reflect.has(userListData,'data') && Array.isArray(userListData.data) && userListData.data.length > 1) {
            // 保存卡信息
            this.goToPageSelectDinner()
            return
          }
          // 多个项目点
          if (canteenListData && Reflect.has(canteenListData,'data') && Array.isArray(canteenListData.data) && canteenListData.data.length > 1) {
            // 保存项目信息
            this.goToPageSelectDinner()
            return
          }
          if (takeMealTypeData && Array.isArray(takeMealTypeData) && takeMealTypeData.length === 1 && Reflect.has(takeMealTypeData[0], 'take_meal') && takeMealTypeData[0].take_meal !== 'waimai') {
            this.SET_SELECT({
              key: 'person',
              data: {
                groups: data.groups,
                name: data.name,
                person_no: data.person_no
              }
            })
            this.SET_SELECT({
              key: 'org',
              data: {
                org_name: data.org_name,
                org_id: data.org_id
              }
            })
            this.SET_SELECT({
              key: 'take_meal_type',
              data: takeMealTypeData[0].take_meal
            })
            this.$miRouter.push({
              path: '/pages_bundle/appoint/appoint_order'
            })
          }
          else {
            this.goToPageSelectDinner()
          }
        } else {
          this.$u.toast(res.msg || '获取点餐信息失败')
        }
      } else {
        this.$u.toast(res.msg || '获取点餐信息失败')
      }
    },
    // 跳转点餐选择页面
    goToPageSelectDinner() {
      this.$miRouter.push({
        path: '/pages_bundle/select/select_diner?type=reservation&appointType=appoint'
      })
    },
    // 跳转洗衣库系统,分H5和微信环境跳转 weixin://dl/business/?t=fna7NQIv8Mj
    async goToWashingSystem() {
      console.log("goToWashingSystem");
      if (this.platform === 'alipay') {
        // 支付宝环境
        this.$toast({ title: '请期待' })
      }
      var resultLink = await this.getWashSystemUrl()
      if (resultLink != null && resultLink.startsWith('weixin')) {
        window.location.href = resultLink
      } else {
        this.$u.toast(resultLink)
      }
    },
    /**
     * 现在第三方直接使用weixin URL Scheme进行跳转
     * weixin://dl/business/?t= *TICKET*
     */
    getWashSystemUrl() {
      var params = {
        company_id: this.companyId ? this.companyId : this.userInfo.company_id,
        person_no: this.userInfo.person_no
      }
      this.$showLoading({
				title: '加载中...',
				mask: true
			})
      return new Promise(resolve => {
        apiBookingZkLaundryGetZkLaundryScheme(params).then(res => {
          uni.hideLoading()
          console.log('res', res);
          if (res.code === 0) {
            var data = res.data || {}
            var openLink = data.openLink ? data.openLink : "跳转失败"
            resolve(openLink)
          } else {
            resolve("跳转失败," + res.msg)
          }
        }).catch(error => {
          uni.hideLoading()
          console.log('error', error);
          resolve('跳转失败' + error.message)
        })
      })
    },
    async getApiGetCouponManageReceiveList() {
      this.$showLoading({
        title: '加载中....',
        mask: true
      })
      const [err, res] = await this.$to(
          apiGetCouponManageReceiveList({
            page: 1,
            page_size: 10000
          })
      )
      uni.hideLoading()
      if (err) {
        // 请求失败,隐藏加载状态
        uni.$u.toast(err.message)
        return
      }
      if (res.code === 0) {
        const results = res.data.results
        let path = ''
        // 有值跳转到卡券中心 没有挑到我的卡券
        if (results.length) {
          path = '/pages_bundle/coupon/index'
        }else {
          path = '/pages_bundle/coupon/my_coupon'
        }
        this.$miRouter.push({
          path: path,
        })
      } else {
        uni.$u.toast(res.msg)
      }
    },
    // 商城获取链接
    async getThirdShow() {
      this.$showLoading({
        title: '请求中...',
        mask: true
      })
      let res = await getApiThirdShow()
      uni.hideLoading()
      // 如果有开户就跳转去农行转入转出页面，没有就返回code=2
      if (res.code === 302) {
        window.location.href = res.data
      } else {
        uni.$u.toast(res.msg)
      }
    },
    getQuestionList() {
      apiGetSurveyInfoList()
        .then(res => {
          uni.hideLoading()
          if (res.code == 0) {
            this.questionList = res.data.results
          } else {
            uni.$u.toast(res.msg)
          }
        })
        .catch(err => {
          uni.hideLoading()
          uni.$u.toast(err.message)
        })
    },
    async gotoQuestionList() {
      // this.$miRouter.push('/pages_common/question/question_list')
      this.$miRouter.push({
        path: '/pages_common/question/question_list',
      })
    },
    // 美团获取链接
    async getThirdMeituan() {
      this.$showLoading({
        title: '请求中...',
        mask: true
      })
      let res = await getApiMeituanLoginFree()
      uni.hideLoading()
      if (res.code === 302) {
        if (res.data.url) {
          window.location.href = res.data.url
        }
      } else {
        uni.$u.toast(res.msg)
      }
    },
    
    //  获取自注册审批列表
    async getAutoRegisterList() {
      let params = {
        // company_id: this.userInfo.company_id
      }
      const [err, res] = await this.$to(apiBookingApproveRegisterGetInfoPost(params))
      if (err) {
        return uni.$u.toast(err.message)
      }
      if (res && res.code === 0) {
        this.autoRegisterList = res.data || []
      } else {
        uni.$u.toast(res.msg || '出错啦！')
      }
    },
    approveAgainHandle(item) {
      this.$miRouter.push({
        path: '/pages/register/auto_register',
        query: {
          company_id: item.company,
          company_name: item.company_name,
          person_no: item.person_no,
          name: item.name,
          phone: item.phone,
          redirect: '/pages/index/index'
        }
      })
    },
    // 关闭弹窗
    closeFirstTipDialog() {
      this.showFirstTipDialog = false
    },
    // 更新人脸开关
    updateSwitchFacepay() {
      return new Promise(resolve => {
        this.$showLoading({
          title: '加载中...',
          mask: true
        })
        getApiUserSwitchFacepay({
          facepay: false
        })
          .then(res => {
            uni.hideLoading()
            if (res.code == 0) {
              // 关闭人脸成功
              console.log("getApiUserSwitchFacepay", res)
              resolve(true)
            } else {
              uni.$u.toast(res.msg)
              resolve(false)
            }
          })
          .catch(err => {
            uni.$u.toast(err)
            resolve(false)
          })
      })
    },
    // 权限有视频公话才调用 获取视频公话的留言信息
    async getUserVoipInfo() {
      const [err, res] = await this.$to(getApiUserVoipInfo())
      if (err) {
        uni.$u.toast(err.message)
        return
      }
      if (res.code == 0) {
        // 需要获取留言箱的留言信息
        if (res.data.unread_message_count) {
          this.$store.dispatch('setNotice', {
              type: 'voip_message',
              data: res.data.unread_message_count
            })
          }
      } else {
        uni.$u.toast(res.msg)
      }
    }
  },
  onUnload() {
    this.clearIntervalFun()
  },
  onHide() {
    this.clearIntervalFun()
  }
  }
</script>

<style lang="scss" scoped>
.index {
  padding-bottom: 40rpx;
  .hidden {
    overflow: hidden;
  }
  .card {
    background: #ffffff;
    border-radius: 20rpx;
    padding: 30rpx;

    .card-title {
      font-size: $font-size-md;
      font-weight: 500;
      color: $color-text-black;
    }
  }
  .swiper-card {
    background: #ffffff;
    border-radius: 20rpx;
    padding: 30rpx 0;
    .card-title {
      font-size: $font-size-md;
      font-weight: 500;
      color: $color-text-black;
    }
    .swiper-wrapper {
      height: 245rpx;
    }
  }
  .banner {
    height: 360rpx;

    .swiper {
      height: 100%;
    }
  }

  .main-content {
    padding: 0 40rpx;
    margin-top: -76rpx;
    // margin-top: -204rpx;
    margin-bottom: 60rpx;
    position: relative;
    z-index: 1;

    .user-info {
      .info-content {
        min-width: 0;
        // border-right: $border-base;
        // padding-right: 30rpx;
        // 顶部个人数据显示
        .info-name {
          min-width: 0;
          display: flex;
          justify-content: space-between;
          align-items: center;
          .card-title-not-project {
            font-weight: bold;
            letter-spacing: 2rpx;
          }
          .line-1 {
            font-size: 32rpx;
            font-weight: bold;
            letter-spacing: 2pxpx;
            margin-bottom: 6rpx;
          }
          .card-title {
            font-size: 24rpx;
            color: #b9bbbd;
            letter-spacing: 5rpx;
            width: 350rpx;
            overflow: hidden;
            white-space: nowrap;
            text-overflow: ellipsis;
            -o-text-overflow: ellipsis;
          }
        }

        .tag {
          flex: none;
          padding: 6rpx 12rpx;
          border-radius: 60rpx;
          color: $color-primary;
          background-color: $color-primary-light-9;
        }
      }
    }

    .menu {
      .menu-lists {
        // padding: 0rpx 10rpx 0;
        // padding: 40rpx 10rpx 0;
        .menu-item {
          width: 20%;
          position: relative;
          .menu-item-point {
            position: absolute;
            top: 0;
            right: 22rpx;
            width: 16rpx;
            height: 16rpx;
            border-radius: 10rpx;
            background-color: $color-red;
          }
        }
        .swiper-dot-wrapper {
          border-radius: 200rpx;
          .swiper-dot {
            width: 15rpx;
            height: 4rpx;
            border-radius: 200rpx;
            margin: 0 4rpx;
            transition: transform 0.3s;
            background-color: #e3e3e3;
            &.active {
              width: 20rpx;
              background-color: $color-primary;
            }
          }
        }
      }
    }

    .notice {
      background: #ffffff;
      border-radius: 20rpx;
      padding: 16rpx 0 16rpx 30rpx;
      position: relative;
      .notice-title {
        border-right: $border-base;
        padding-right: 14rpx;

        .notice-system {
          width: 58rpx;
          height: 25rpx;
        }

        .notice-tag {
          // background-image: url("https://packer-static-assets.oss-cn-shenzhen.aliyuncs.com/mini/52bdc15374146d4de517b11b43c261621675755522126.png");
          // width: 64rpx;
          // height: 30rpx;
          text-align: center;
          // line-height: 30rpx;
          // padding-right: 10rpx;
          // background-size: 100%;
          color: #fff;
          background-color: $color-primary;
          padding: 4rpx 4rpx 2rpx 6rpx;
          border-radius: 8rpx;
          font-size: 24rpx;
          letter-spacing: 2rpx;
          line-height: 24rpx;
        }
      }
      .notice-num {
        position: absolute;
        top: 34rpx;
        right: 50rpx;
        background-color: $color-red;
        color: #ffffff;
        padding: 0 8rpx;
        border-radius: 20rpx;
        font-size: 22rpx;
      }
    }
    .diet-report {
      background-color: #ffffff;
      border-radius: 20rpx;
      overflow: hidden;
      .meal-report-title {
        font-size: 30rpx;
        letter-spacing: 1rpx;
      }
      .diet-report-top {
        height: 29rpx;
        width: 620rpx;
        justify-content: space-between;
        margin: 30rpx auto;
        .slot-content {
          font-size: 32rpx;
          line-height: 48rpx;
          color: #1d201e;
        }
      }
      .diet-report-but {
        width: 174rpx;
        height: 50rpx;
        border-radius: 25rpx;
        color: $color-white;
        margin: 30rpx auto;
      }
      .report-center-left {
        display: flex;
        flex-direction: column;
        justify-content: space-evenly;
      }
      .report-center-right {
        position: relative;
        width: 106rpx;
        height: 106rpx;
        image {
          width: 106rpx;
          height: 106rpx;
        }
        .report-center-right-num {
          width: 52rpx;
          height: 32rpx;
          background-color: #1d201e;
          border-radius: 8rpx 0rpx 12rpx 0rpx;
          opacity: 0.6;
          color: #fff;
          position: absolute;
          right: 0;
          bottom: 0;
        }
      }
      .divider {
        width: 610rpx;
        height: 1rpx;
        margin: 20rpx auto 0;
        background-color: #efefef;
      }
    }
    .dietary-energy {
      .meal-power {
        display: flex;
        align-items: center;
        justify-content: space-between;
      }
      .meal-power-title {
        font-weight: bold;
        font-size: 30rpx;
        letter-spacing: 1rpx;
        margin-bottom: 3rpx;
      }
      .power-btn-text {
        display: inline-block;
        text-align: center;
        padding-left: 6rpx;
        line-height: 1;
      }
      .energy {
        .add_energy {
          width: 200rpx;
          text-align: center;
          border: 2rpx solid #d7d7d7;
          border-radius: 10rpx;
        }
        .energy-item {
          flex: 1;
          transition: all 1s;
          overflow: hidden;
          .energy-ball {
            // background: url($imgBasePath + '/images/energy_ball.png');
            width: 112rpx;
            height: 112rpx;
            background-size: 100% 100%;
            border-radius: 50%;
          }
          .energyEggName {
            text-align: center;
          }
        }

        .energy-item:nth-child(1) {
          animation: energy-ball 3.4s infinite;
        }

        .energy-item:nth-child(2) {
          animation: energy-ball 2.8s infinite;
        }

        .energy-item:nth-child(3) {
          animation: energy-ball 3.6s infinite;
        }

        .energy-item:nth-child(4) {
          animation: energy-ball 3.2s infinite;
        }

        @keyframes energy-ball {
          0% {
            transform: translateY(0);
          }

          50% {
            transform: translateY(14rpx);
          }

          100% {
            transform: translateY(0rpx);
          }
        }
      }
      .caloric-difference {
        padding: 15rpx;
        background-color: #f6f7fb;
        border-radius: 13rpx;
        .icon-hot {
          padding-right: 10rpx;
          color: $color-yellow
        }
      }
    }

    .card-menu {
      &__item {
        padding: 32rpx 30rpx;
        width: 325rpx;
        box-sizing: border-box;
        background-repeat: no-repeat;
        background-size: 100% 100%;

        // &:not(:nth-of-type(2n)) {
        // 	margin-right: 22rpx;
        // }
        &.healthy {
          // background-image: url($imgBasePath + '/images/health_assessment.png');
        }

        // &.body {
        //   background-image: url('https://packer-static-assets.oss-cn-shenzhen.aliyuncs.com/v4/61a177997b78cf7928a76ed5753142511656645414853.png');
        // }
        &.dietary {
          background-image: url('https://packer-static-assets.oss-cn-shenzhen.aliyuncs.com/v4/9423a3dbdebfb6545684a051eaa7c3741656645492284.png');
        }
      }
    }

    .health-information {
      .information-lists {
        .information-item {
          padding: 20rpx 0;

          &:not(:last-of-type) {
            border-bottom: $border-base;
          }
        }
      }
    }
    .question-banner{
      width: 100%;
      img{
        width: 100%;
      }
    }
    .company_empty {
      margin-top: 20rpx;
      height: 150rpx;
      background-color: #fff;
      border-radius: 15rpx;
      text-align: center;
      line-height: 150rpx;
    }
    .auto-register {
      .register-item {
        padding: 30rpx;
        border-radius: 20rpx;
        background-color: #fff;
      }
      
      .register-title {
        padding-bottom: 20rpx; 
      }
      .register-content {
        padding: 20rpx 0;
        border-top: 1px solid $border-color-light;
        border-bottom: 1px solid $border-color-light;
      }
      .register-content-text{
        padding: 10rpx 0;
        line-height: 1.5;
      }
      .register-footer {
        margin-top: 20rpx;
        min-height: 26rpx;
      }
    }
  }
  // 钱包余额
  .wallet-balance {
    background-color: #fff;
    border-radius: 20rpx;
    margin-top: 20rpx;
    padding: 30rpx;
    // 钱包余额内容区
    .wallet-balance-content {
      display: flex;
      justify-content: space-between;
      padding-bottom: 30rpx;
      // border-bottom: 2rpx solid #f5f2f2;
      // 内容区左侧顶部
      .content-t {
        font-size: 28rpx;
        .t-l {
          font-size: 32rpx;
          font-weight: bold;
          margin-right: 10rpx;
          vertical-align: middle;
        }
        .t-r {
          color: red;
          font-weight: normal;
        }
      }
      // 内容区左侧中部
      .content-m {
        .content-mid-l {
          display: inline-block;
          margin-top: 20rpx;
          font-size: 52rpx;
          font-weight: bold;
        }
      }
      // 内容区右侧
      .wallet-balance-content-r {
        margin-top: 60rpx;
        .memberBtn {
          width: 140rpx;
          height: 58rpx;
          line-height: 58rpx;
          letter-spacing: 4rpx;
        }
      }
    }
    // 钱包余额底部
    .wallet-balance-footer {
      height: 36rpx;
      margin-top: 30rpx;
      font-size: 32rpx;
      overflow: hidden;
      .wallet-footer-item {
        display: flex;
        justify-content: space-between;
      }
    }
  }
  // 用餐订单
  .meal-order {
    background: #fff2e6 $bg-linear-gradient-3;
    border-radius: 20rpx;
    margin-top: 20rpx;
    padding: 30rpx;
    height: 280rpx;
    overflow: hidden;
    // 轮播图
    .meal-swiper {
      height: 90%;
    }
    // 用餐订单顶部文字
    .content-t {
      .t-text {
        font-size: 26rpx;
        color: black;
        .order-num {
          color: red;
        }
      }
    }
    // 用餐订单主要内容
    .content-main {
      background-color: #fff;
      height: 150rpx;
      border-radius: 16rpx;
      margin-top: 20rpx;
      // 主要内容中的卡片
      .main-card {
        padding: 40rpx;
        display: flex;
        justify-content: space-between;
        align-items: center;
        .main-card-l {
          .meal-type {
            display: inline-block;
            color: #fff;
            font-size: 24rpx;
            padding: 4rpx 8rpx 0px 8rpx;
            background-color: #8da5f4;
            border-radius: 6rpx;
          }
          .meal-name {
            font-size: 30rpx;
            font-weight: bold;
            margin-left: 10rpx;
            vertical-align: text-top;
            letter-spacing: 2rpx;
          }
          .meal-num {
            margin-top: 4rpx;
            font-size: 24rpx;
            color: #9ea0a3;
          }
        }
      }
    }
  }
  .scroll-view_H {
    height: 400rpx;
  }

  .page__radio-item {
    width: 550rpx;
  }

  .mask-btn {
    display: flex;
    justify-content: space-between;
    padding-top: 20rpx;
  }
  // .charts-box {
  //    width: 100%;
  //    height: 300px;
  //  }
  .weeklyPopup {
    ::v-deep .u-popup__content {
      background-color: unset;
    }
    .weeklyNew {
      // background-image: url($imgBasePath + '/images/tcbg.png');
      height: 728rpx;
      width: 640rpx;
      background-size: 100% 100%;
      background-repeat: no-repeat;
      position: relative;
      .newContent {
        position: absolute;
        top: center;
        left: center;
        .date-range {
          border-radius: 16rpx;
        }
        .text {
          width: 65%;
          text-align: center;
        }
        .button {
          width: 300rpx;
          height: 80rpx;
          // background-image: url($imgBasePath + '/member/reportButton.png');
          background-size: 100% 100%;
          background-repeat: no-repeat;
        }
      }
    }
  }
  .weeklyNew {
    // background-image: url($imgBasePath + '/images/tcbg.png');
    height: 728rpx;
    width: 640rpx;
    background-size: 100% 100%;
    background-repeat: no-repeat;
    position: relative;
    .newContent {
      position: absolute;
      top: 45%;
      left: 50%;
      transform: translateX(-50%);
      .date-range {
        border-radius: 16rpx;
      }
      .text {
        width: 65%;
        text-align: center;
      }
      .button {
        width: 300rpx;
        height: 80rpx;
        // background-image: url($imgBasePath + '/member/reportButton.png');
        background-size: 100% 100%;
        background-repeat: no-repeat;
      }
    }
  }
  // 订单二维码弹窗
  .order-qrcode-popup {
    .order-qrcode-title {
      color: #fff;
      font-size: 38rpx;
      background-color: $color-primary;
      height: 90rpx;
      margin-bottom: 40rpx;
      line-height: 90rpx;
      text-align: center;
      letter-spacing: 22rpx;
      border-radius: 10rpx 10rpx 0 0;
    }
    .order-qrcode-content {
      display: flex;
      flex-direction: column;
      align-items: center;
      // 订单二维码弹窗 --- 出示二维码提醒文字
      .order-text-remind {
        margin-top: 30rpx;
        margin-bottom: 30rpx;
        font-size: 32rpx;
        letter-spacing: 2rpx;
      }
    }
  }
}
// .u-nav-slot {
//   display: flex;
//   flex-direction: row;
//   align-items: center;
//   justify-content: space-between;
//   border-width: 0.5px;
//   border-radius: 100px;
//   border: 1px solid #DADBDF;
//   padding: 3px 7px;
//   opacity: .8;
//   .u-icon--right {
//     flex-direction: row;
//     align-items: center;
//   }
// }
.agreement-text {
  margin-top: 100rpx;
  font-size: 26rpx;
}
</style>
