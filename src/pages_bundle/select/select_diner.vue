<!-- 选择就餐人 -->
<template>
  <view :style="theme.style" class="select-diner">
    <!-- #ifndef MP-ALIPAY -->
    <u-navbar bg-color="transparent" left-icon-color="#fff" @leftClick="topLeftClick">
      <view class="white lg f-w-500" slot="center">选择预约就餐信息</view>
    </u-navbar>
    <!-- #endif -->
    <!-- #ifdef MP-ALIPAY -->
    <!-- ALIPAY给个占位吧 -->
    <view class="white lg f-w-500" style="height: 46rpx;"></view>
    <!-- #endif -->
    <template>
      <view class="choice-title" v-if="personnelList && personnelList.length">请选择就餐人</view>
      <!-- 人选 -->
      <u-radio-group
        v-model="personnelCheckedValue"
        shape="square"
        iconPlacement="right"
        placement="column"
        @change="personnelCheckboxChange"
      >
        <block v-for="personnelItem in personnelList" :key="personnelItem.person_no">
          <view class="select-diner-item">
            <u-radio :disabled="isCodeDisabled" :activeColor="variables.colorPrimary" :name="personnelItem.person_no" :customStyle="groupCustomStyle">
              <view class="flex" style="padding: 40rpx 0">
                <!-- 组件只能套多一层 -->
                <text class="img-filter">
                  <u-image  width="40rpx" height="40rpx" :src="themeImgPath.img_bundle_icon_diner"></u-image>
                </text>
                <view class="m-l-18 lg f-w-500">
                  {{ personnelItem.name }}
                </view>
                <view v-if="personnelItem.person_no" class="md f-w-500 muted">（{{ personnelItem.person_no }}）</view>
              </view>
            </u-radio>
          </view>
        </block>
      </u-radio-group>
    </template>
    <template v-if="orgDataList && orgDataList.length">
      <view class="choice-title">请选择食堂</view>
      <!-- 食堂 -->
      <u-radio-group
        v-model="orgCheckedValue"
        shape="square"
        iconPlacement="right"
        placement="column"
        @change="orgCheckboxChange"
      >
        <block v-for="orgItem in orgDataList" :key="orgItem.org_id">
          <view class="select-diner-item">
            <u-radio :disabled="isCodeDisabled" :activeColor="variables.colorPrimary" :name="orgItem.org_id" :customStyle="groupCustomStyle">
              <view class="flex" style="padding: 40rpx 0">
                <text class="img-filter">
                  <u-image width="40rpx" height="40rpx" :src="themeImgPath.img_bundle_icon_canteen"></u-image>
                </text>
                <view class="m-l-18 lg f-w-500 line-2">
                  {{ orgItem.org_name }}
                </view>
              </view>
            </u-radio>
          </view>
        </block>
      </u-radio-group>
    </template>
    <template v-if="selectHallFood && selectHallFood.length">
      <view class="choice-title">取餐方式</view>
      <!-- 取餐方式 -->
      <u-radio-group
        v-model="hallCheckedValue"
        shape="square"
        iconPlacement="right"
        placement="column"
        @change="hallCheckboxChange"
      >
        <block v-for="hallItem in selectHallFood" :key="hallItem.take_meal">
          <view class="select-diner-item">
            <u-radio :disabled="isCodeDisabled" :activeColor="variables.colorPrimary" :name="hallItem.take_meal" :customStyle="groupCustomStyle">
              <view class="flex" style="padding: 40rpx 0">
                <!-- 	<u-image width="40rpx" height="40rpx" :src="hallItem.icon">
								</u-image> -->
                <view class="m-l-18 lg f-w-500">
                  {{ hallItem.take_meal_alias }}
                </view>
              </view>
            </u-radio>
          </view>
        </block>
      </u-radio-group>
    </template>
		<template v-if="showAddressName">
			<view class="address-name">
				<view style="width: 180rpx;">配送地址:</view>
				<view>{{addressInfo.full_addr_name}}</view>
			</view>
		</template>
		<template v-if="showToSelectAddress">
			<view class="select-address m-t-20 ls-card bg-white md">
				<view class="item bb flex row-between" @click="showAddressPopup = true">
					<view style="width: 180rpx;" class="md">配送地址：</view>
					<view class="flex row-right">
						<view class="md black">{{ addressInfo.full_addr_name }}</view>
						<u-icon color="#999" name="arrow-right" size="24rpx"></u-icon>
					</view>
				</view>
			</view>
		</template>
    <!-- 确定按钮 -->
    <view class="diner-determine" v-if="personnelCheckedValue && orgCheckedValue && hallCheckedValue">
      <u-button
        text="确定"
        shape="circle"
        :color="variables.bgLinearGradient1"
        @click="handleConfirm"
      ></u-button>
    </view>
    <!-- 弹窗 -->
    <popup></popup>
    <floating-popup :floatingPopupShow="floatingPopupShow"></floating-popup>
    <u-modal
      :show="showModal"
      :title="modalTitle"
      :content="modalText"
      width="280px"
      :showCancelButton="true"
      :confirmColor="variables.colorPrimary"
      :cancelText="modalCancelText"
      @confirm="confirmModal"
      @cancel="cancelModal"
      class="confirm-modal"
      >
				<view class="slot-content">
          <u-icon @click="hideModal" class="modal-close-btn" color="#999" name="close" size="34rpx"></u-icon>
					<rich-text :nodes="modalText"></rich-text>
				</view>
		</u-modal>
		<address-select
		  :show-address-popup.sync="showAddressPopup"
			:addr-id="addressInfo.addr_center_id"
			:org="orgCheckedValue"
			@confirm="confirmAddress"
		>
		</address-select>
  </view>
</template>

<script>
import { getApiBookingUserGetCardUserList, getApiBookingUserGetCanteenList, getApiTakeMealTypeList, getApiGetAddrDetails, getApiGetAddrList } from '@/api/reservation'
import { getApiUserGetProjectCardUserList, setApiChangeProjectPoint } from '@/api/app'
import { reportShopcardClean } from '@/api/report_meal.js'
import { apiShopcardClean } from '@/api/shopcart'
import Cache from '@/utils/cache'
import { mapMutations, mapActions, mapGetters } from 'vuex'
import { apiQueryUserinfo, apiGetUserAddress } from '@/api/user.js'
import AddressSelect from '../components/address-select/address-select.vue'
import { formateVisitorParams, encodeQuery, deepClone } from "@/utils/util.js"
import { apiGetUserOpenid, apiGetWechatValidate, apiGetVisitorConfig } from '@/api/app'
import { object } from '@dcloudio/vue-cli-plugin-uni/packages/postcss/tags'
import { apiBookingUserGetUserOrderSetting } from '../../api/order'
import FloatingPopup from '@/components/floating-popup/floatingPopup.vue'

export default {
	components: {
	  AddressSelect,
    FloatingPopup
	},
  data() {
    return {
      imgPath: this.$imgPath,
      personnelCheckedValue: '',
      orgCheckedValue: 0,
      hallCheckedValue: '',
      type: '',
      userinfo: {},
      personnelList: [], //就餐人数据
      orgDataList: [], //组织数据
      // getCanteenUserListData: {}, //接口返回的数据 需要处理
      groupCustomStyle: {
        width: '100%',
        display: 'flex',
        'justify-content': 'space-between'
      },
      // 具体取餐方式-堂食列表
      selectHallFood: [
        // {
        // 	id: '',
        // 	name: '堂食',
        // 	type: "on_scene",
        // 	icon: this.$imgPath.img_bundle_icon_hall_food,
        // 	iconSelect: this.$imgPath.img_bundle_icon_hall_food_white,
        // },
        // {
        // 	id: '2',
        // 	type: "bale",
        // 	name: '自提打包',
        // 	icon: this.$imgPath.img_bundle_icon_take_out_food,
        // 	iconSelect: this.$imgPath.img_bundle_icon_take_out_food_white,
        // },
      ],
      personNoSelect: {},
			
			// 关于扫码的
      codeNo: '',
      codeType: '',
      codeCompanyId: '',
			isCodeDisabled: false, // 扫码进来不给切换组织、取餐方式等信息
      showModal: false,
      modalType: '',
			modalTitle: '提示',
      modalText: '',
      modalCancelText: '',
			relatedOrg: {}, // 地址码的创建组织
			showAddressName: false,
      companyUserInfo: {},
			// 关于选择地址的
			showToSelectAddress: false, // 控制显示选择地址的
			addressList: [],
			showAddressPopup: false, // 显示地址选择器
			defaultAddressValue: [],
      allowVisitorlogin: false, // 游客配置，是否允许游客点餐
      reservationInfo: {}, // 预约点餐数据缓存
      floatingPopupShow: false
    }
  },
	computed: {
    ...mapGetters(['codeInfo', 'isAddressVisitor']),
		addressInfo() { // vuex保存的地址信息，因为有多次会改变address_info的情况，姑且放在计算属性中吧
			if (this.$store.state.appoint.select.address_info) { // 保存的地址信息
				if (this.$store.state.appoint.select.address_info.type === 'code') { // 从点餐页面进来的，又是扫码，不能修改组织、取餐方式等信息，禁用掉
					this.isCodeDisabled = true
				}
				return this.$store.state.appoint.select.address_info
			} else {
				return {}
			}
		}
	},
  methods: {
    ...mapMutations(['SET_SELECT']),
    ...mapActions({
      setUserInfo: 'setUserInfo',
      setAddressVisitor: 'setAddressVisitor',
      setCodeInfo: 'setCodeInfo'
    }),
		topLeftClick() {
			if (this.codeInfo || this.addressInfo) { // 扫码进来，如果要返回，就返回到主页
				// 把点餐的address_info清空掉
				this.SET_SELECT({
				  key: 'address_info',
				  data: {}
				})
        if (this.isAddressVisitor) {
          this.$miRouter.replaceAll('/pages/user/user')
        } else {
          this.$miRouter.replaceAll('/pages/index/index')
        }
				
			} else { // 其他情况返回到上一页
				this.$miRouter.back()
			}
		},
    getBookingUserGetCardUserList() {
      this.$showLoading({
        title: '获取中....',
        mask: true
      })
      getApiBookingUserGetCardUserList(formateVisitorParams({
        company_id: this.userinfo.company_id
      }))
        .then(res => {
          uni.hideLoading()
          if (res.code == 0) {
            this.personnelList = res.data
						if (this.codeType === "address") { // 如果是地址码扫码点餐直接进来的,默认选下
              if (this.isAddressVisitor) { // 游客
                this.personnelList = [{ name: '游客', person_no: 'visitor' }]
              }
              this.personnelCheckedValue = this.personnelList[0].person_no
              this.personNoSelect = this.personnelList[0]
              this.SET_SELECT({
                key: 'person',
                data: this.personNoSelect
              })
              this.getBookingUserGetCanteenList(this.personnelList[0])
						} else { // 如果是普通点餐或者扫码后从点餐页返回的
              if (this.isAddressVisitor) { // 游客扫码从点餐页回来
                this.personnelList = [{ name: '游客', person_no: 'visitor' }]
              }
							// 有默认的话给默认
							if (this.$store.state.appoint.select.person.person_no) {
								// this.personNoSelect = this.$store.state.appoint.select.person
								// 找到旧的赋值，没有的取消赋值
								const personData = this.personnelList.filter(item => item.person_no == this.$store.state.appoint.select.person.person_no)
								this.personNoSelect = personData[0]
								if (personData.length) {
									this.personnelCheckedValue = personData[0].person_no
									this.getBookingUserGetCanteenList(personData[0])
								} else {
									this.personNoSelect = ''
									this.SET_SELECT({
										key: 'person',
										data: {}
									})
								}
							}
						}
          } else {
            uni.$u.toast(res.msg)
          }
        })
        .catch(error => {
          uni.hideLoading()
          uni.$u.toast(error.message)
        })
    },
    // 获取食堂组织
    getBookingUserGetCanteenList(personData) {
      let params = {
        groups: personData.groups,
        company_id: this.userinfo.company_id,
        user_id: this.userinfo.user_id,
        h5_type: this.type === 'report_meal' ? 'report_meal' : ''
      }
      getApiBookingUserGetCanteenList(formateVisitorParams(params))
        .then(res => {
          uni.hideLoading()
          if (res.code == 0) {
            this.orgDataList = res.data
						// 如果是地址码扫码点餐进来的,默认选下this.relatedOrg.org_id
						if (this.codeType === "address") {
              this.orgCheckedValue =this.relatedOrg.org_id
              this.SET_SELECT({
                key: 'org',
                data: this.relatedOrg
              })
              if (this.isAddressVisitor) {
                this.getTakeMealTypeList('', this.relatedOrg.org_id)
              } else {
                this.getTakeMealTypeList(personData, this.relatedOrg.org_id)
              }
						} else {
							// 有默认的话给默认
							if (this.$store.state.appoint.select.org.org_id) {
								// 找到旧的赋值，没有的取消赋值
								const orgDataFilterList = res.data.filter(item => item.org_id == this.$store.state.appoint.select.org.org_id)
								if (orgDataFilterList.length) {
									this.orgCheckedValue = orgDataFilterList[0].org_id
									// this.getBookingUserGetCanteenList(orgDataFilterList[0])
									this.getTakeMealTypeList(personData, orgDataFilterList[0].org_id)
								} else {
									this.orgCheckedValue = 0
									this.SET_SELECT({
										key: 'org',
										data: {}
									})
								}
							}
						}
          } else {
            uni.$u.toast(res.msg)
          }
        })
        .catch(error => {
          uni.$u.toast(error.message)
        })
    },
    // 获取堂食方式
    getTakeMealTypeList(personData, orgId) {
      console.log("getTakeMealTypeList", personData , orgId);
      let params
      if (this.isAddressVisitor) {
        params = formateVisitorParams({
          organization_id: orgId,
          company_id: this.codeCompanyId,
        })
      } else {
        params = {
          groups: personData.groups,
          organization_id: orgId,
          h5_type: this.type === 'report_meal' ? 'report_meal' : ''
        }
      }
      getApiTakeMealTypeList(params)
        .then(res => {
          uni.hideLoading()
          if (res.code == 0) {
            
            this.selectHallFood = res.data
            // 如果是地址码扫码点餐进来的
            if (this.codeType === "address") {
              if (this.selectHallFood.find(item => item.take_meal === 'waimai')) {
                this.hallCheckedValue = 'waimai'
                // this.getPreAddress()
								if (this.addressInfo.can_modify_addr) {  // 扫码允许修改地址
									this.showToSelectAddress = true
								} else {
									this.showAddressName = true
								}
								this.SET_SELECT({
								  key: 'take_meal_type',
								  data: this.hallCheckedValue
								})
              } else { // 如果没有外卖选项
								uni.$u.toast('当前项目点暂未开通外卖地址码点餐')
							}
            } else {
              // 有默认的话给默认
              if (this.$store.state.appoint.select.take_meal_type) {
                // 找到旧的赋值，没有的取消赋值
                const selectHallFoodFilterList = res.data.filter(
                  item => item.take_meal == this.$store.state.appoint.select.take_meal_type
                )
                if (selectHallFoodFilterList.length) {
                  this.hallCheckedValue = selectHallFoodFilterList[0].take_meal
									if (!this.addressInfo.can_modify_addr && this.addressInfo.type === 'code') {
										this.showAddressName = true
									} else if (this.hallCheckedValue === 'waimai'){ // 如果是外卖，或者从点餐页再次进入当前页，且扫码支持修改地址的，需要显示地址选择
										this.showToSelectAddress = true
									}
                } else {
                  this.hallCheckedValue = ''
                  this.SET_SELECT({
                    key: 'take_meal_type',
                    data: ''
                  })
                }
                if (this.addressInfo.type !== 'code' && this.hallCheckedValue === 'waimai') {
                  this.getPreAddress()
                }
              }
            }
          } else {
            uni.$u.toast(res.msg)
          }
        })
        .catch(error => {
          uni.$u.toast(error.message)
        })
    },
    // 选中回调
    personnelCheckboxChange(value) {
      // 根据人员编号去拿到对应的数据
      this.personNoSelect = this.personnelList.find(item => item.person_no == value)
      this.getBookingUserGetCanteenList(this.personNoSelect)
      this.SET_SELECT({
        key: 'person',
        data: this.personNoSelect
      })
      // 重置数据
      this.orgCheckedValue = 0
      this.hallCheckedValue = ''
      this.selectHallFood = []
      this.orgDataList = []
      this.SET_SELECT({
        key: 'take_meal_type',
        data: ''
      })
      this.SET_SELECT({
        key: 'org',
        data: {}
      })
    },
    orgCheckboxChange(value) {
      // 根据人员编号去拿到对应的数据
      // this.selectHallFood = this.canteenUserData(this.$store.state.appoint.select.person.person_no, this
      // 	.getCanteenUserListData.take_meal_type)
      this.getTakeMealTypeList(this.personNoSelect, value)
      const select = this.orgDataList.find(item => item.org_id == value)
      this.SET_SELECT({
        key: 'org',
        data: select
      })
			// 切换组织重新获取地址信息，不用管扫码，扫码切换不了组织
			this.SET_SELECT({
			  key: 'address_info',
			  data: {}
			})
			this.defaultAddressValue = []
			this.showToSelectAddress = false
			// this.getAddressList()
      // 切换食堂清空报餐购物车
      if (this.type == 'report_meal') {
        this.delMealReportShopCard()
      }
      if (this.type == 'reservation') {
        this.clearCard()
      }
      // 切换清理缓存
      this.hallCheckedValue = ''
      this.SET_SELECT({
        key: 'take_meal_type',
        data: ''
      })
    },
    // 清空报餐全部购物车
    delMealReportShopCard() {
			if (!this.hallCheckedValue) return
      this.$showLoading({
        mask: true
      })
      reportShopcardClean({
        payment_order_type: 'report_meal',
        take_meal_type: this.hallCheckedValue
      })
        .then(res => {
          uni.hideLoading()
        })
        .catch(error => {
          uni.$u.toast(error.message)
        })
    },
    clearCard() {
			if (!this.hallCheckedValue) return
      this.$showLoading({
        mask: true
      })
      apiShopcardClean(formateVisitorParams({
        payment_order_type: this.type,
        take_meal_type: this.hallCheckedValue
      }))
        .then(res => {
          uni.hideLoading()
        })
        .catch(err => {})
    },
    hallCheckboxChange(value) {
      console.log("hallCheckboxChange", value);
			if (value === 'waimai') { // 外卖需要显示地址
				this.showToSelectAddress = true
        this.getPreAddress()
			} else {
				this.showToSelectAddress = false
			}
      this.SET_SELECT({
        key: 'take_meal_type',
        data: value
      })
    },
    handleConfirm() {
      if (!this.personnelCheckedValue && !this.orgCheckedValue && !this.hallCheckedValue) {
        return uni.$u.toast('请选择就餐信息')
      }
      if (!this.addressInfo.addr_center_id && this.hallCheckedValue === 'waimai') {
      	return uni.$u.toast('请选择配送地址')
      }
      // 是外卖类型就清空购物车 --张华杰 做电梯费的时候
      if (this.hallCheckedValue === 'waimai') {
        this.clearCard()
      }
      if (this.type === 'report_meal') {
        if (Number(this.$Route.query.meal_pack) === 1) { // 去餐包
          this.$miRouter.replace({
            path: '/pages_bundle/meal_report/meal_package'
          })
        } else { // 普通报餐
          this.$miRouter.replace({
            path: '/pages_bundle/meal_report/meal_report'
          })
        }
      } else {
        this.$miRouter.replace({
          path: '/pages_bundle/appoint/appoint_order'
        })
      }
      // this.$miRouter.replace({
      //   path: this.type == 'report_meal' ? '/pages_bundle/meal_report/meal_report' : '/pages_bundle/appoint/appoint_order'
      // })
    },
    // 扫码进来的时候,校验下项目点
    async gotoCheckCompany() {
      if (this.codeCompanyId === this.userinfo.company_id) {
        if (this.codeType === "address") {
          this.getAddressDetail(this.userinfo.company_id)
        }
      } else {
        // 不是同一个项目点，获取能否游客点餐
        await this.getVisitorSetting()
        // 获取下用户已绑定的项目点
        this.getUserGetProjectCardUserList()
      }
    },
    async getVisitorSetting() {
      await apiGetVisitorConfig({
        addr_id: this.codeNo
      })
        .then(res => {
          if (res.code == 0) {
            if (res.data.support_reservation) {
              this.allowVisitorlogin = true
            } else {
              this.allowVisitorlogin = false
            }
          } else {
            uni.$u.toast(res.msg)
          }
        })
        .catch(err => {
          uni.$u.toast(err)
        })
    },
    // 获取用户已绑定的项目点
    getUserGetProjectCardUserList() {
      getApiUserGetProjectCardUserList()
        .then(res => {
          uni.hideLoading()
          if (res.code == 0) {
            this.companyUserInfo = res.data.find(item => item.company_id === this.codeCompanyId)
            if (this.companyUserInfo) { // 用户存在该项点
              this.modalType = 'changeCompany'
							this.modalTitle = '提示'
              if (this.allowVisitorlogin) {
                this.modalCancelText = '直接点餐'
              } else {
                this.modalCancelText = '取消'
              }
              this.modalText = '您所扫的二维码不属于当前项目点，是否切换项目点？'
              this.showModal = true
            } else {
              // 再让用户选择要不要去添加项目点
              this.showModal = true
              this.modalType = 'bindCompany'
							this.modalTitle = '提示'
							if (this.allowVisitorlogin) {
							  this.modalCancelText = '直接点餐'
							} else {
							  this.modalCancelText = '取消'
							}
              this.modalText = '您尚未绑定该二维码所属的项目点，是否前往绑定？'
            }
          } else {
            uni.$u.toast(res.msg)
          }
        })
        .catch(err => {
          uni.$u.toast(err.message)
        })
    },
    confirmModal() {
      if (this.modalType === 'changeCompany') {
        this.changeCompany() // 切换项目点
      } else if (this.modalType === 'bindCompany') {
        // 用户不存在该项目点，前去绑定项目点的话，清除码的缓存先，用户如果要扫码点餐就让他再扫一次
        this.setCodeInfo(null)
        // 把点餐的address_info清空掉
        this.SET_SELECT({
          key: 'address_info',
          data: {}
        })
        this.$miRouter.replace({
          path: '/pages_bundle/switch_items/switch_items'
        })
      } else if (this.modalType === 'addressDetail') {
				this.showModal = false
			}
    },
    cancelModal() {
      if ((this.modalType === 'changeCompany' || this.modalType === 'bindCompany') && this.allowVisitorlogin) { // 直接点餐
        this.getWechatValidate()
      } else {
        this.hideModal()
      }
    },
    hideModal() {
      this.showModal = false
        this.setCodeInfo(null)
        // 把点餐的address_info清空掉
        this.SET_SELECT({
          key: 'address_info',
          data: {}
        })
        if (this.isAddressVisitor) {
          this.$miRouter.replaceAll('/pages/user/user')
        } else {
          this.$miRouter.replaceAll('/pages/index/index')
        }
    },
    // 切换项目点
    changeCompany() {
      setApiChangeProjectPoint({
        company_id: this.companyUserInfo.company_id,
        company_name: this.companyUserInfo.company_name,
        name: this.companyUserInfo.name,
        person_no: this.companyUserInfo.person_no
      })
        .then(res => {
          uni.hideLoading()
          if (res.code == 0) {
            this.queryUserinfo()
          } else {
            uni.$u.toast(res.msg)
          }
        })
        .catch(err => {
          uni.$u.toast(err.message)
        })
    },
    // 重新登录然后保存个人信息
    queryUserinfo() {
      apiQueryUserinfo()
        .then(res => {
					if (res.code == 0) {
					  this.showModal = false
					  this.setUserInfo(res.data)
            // 去首页重新登陆授权，获取当前项目点的配置
            this.$miRouter.replaceAll({ path: `/pages/index/index?company_id=${res.data.company_id}` })
					  // // 项目点切换成功后,判断码的类型,走去获取详情
					  // if (this.codeType === "address") {
					  //   this.getAddressDetail(res.data.company_id)
					  // }
					} else {
					  uni.$u.toast(res.msg)
					}
        })
        .catch(err => {
          console.log(err)
        })
    },
    getPreAddress() {
      if (this.isAddressVisitor) return
      apiGetUserAddress({
				card_info_id: this.userinfo.use_card,
        org_id: this.orgCheckedValue
			})
        .then(res => {
          uni.hideLoading()
          if (res.code == 0) {
            var data = res.data || {}
            if (data && typeof data === 'object' && Object.keys(res.data).length >0) {
              this.SET_SELECT({
                key: 'address_info',
                data: {
                  type: this.addressInfo.type, // type用于区分扫码或者非扫码
                  addr_center_id: res.data.addr_center,
                  full_addr_name: res.data.parent_names_list.join('-'),
                  can_modify_addr: this.addressInfo.can_modify_addr,
                  company_id: this.addressInfo.company_id
                }
              })
            }else {
              // 没有返回地址清空一下地址数据咯
              this.SET_SELECT({
                key: 'address_info',
                data: {
                  type: this.addressInfo.type, // type用于区分扫码或者非扫码
                  addr_center_id: '',
                  full_addr_name: '',
                  can_modify_addr: this.addressInfo.can_modify_addr,
                  company_id: this.addressInfo.company_id
                }
              })
            }
          } else {
            uni.$u.toast(res.msg)
          }
        })
        .catch(err => {
          uni.$u.toast(err.message)
        })
    },
    // 获取地址详情
    getAddressDetail(company_id) {
      let params = {
				addr_id: this.codeNo,
				company_id: company_id
			}
      
      getApiGetAddrDetails(formateVisitorParams(params))
        .then(res => {
          if (res.code == 0) {
						this.relatedOrg = {
							org_id: res.data.related_org.org_id,
							org_name: res.data.related_org.org_name
						}
						this.SET_SELECT({
						  key: 'address_info',
						  data: {
								type: 'code', // type用于区分扫码或者非扫码
								addr_center_id: this.codeNo,
								full_addr_name: res.data.addr_name,
								can_modify_addr: res.data.related_org.org_settings.can_modify_addr,
                company_id: this.codeCompanyId
							}
						})
						this.showModal = true
						this.modalType = 'addressDetail'
						this.modalTitle = '请确认您的配送地址'
						this.modalCancelText = '取消'
						this.modalText = `所属组织：${res.data.related_org.org_name}<br/>${res.data.addr_name}`
						// this.getBookingUserGetCardUserList()
            // 扫码点餐改成三個接口合一
            this.getBookingUserGetUserOrderSetting()
          } else {
            uni.$u.toast(res.msg)
          }
          // 只要调用过地址详情接口就清缓存，后续根据address_info走
          this.setCodeInfo(null)
        })
        .catch(err => {
          console.log(err)
        })
    },
    // 关于地址选择器
		confirmAddress(e) {
			this.defaultAddressValue = e.value
			this.showAddressPopup = false;
      if (this.addressInfo.type === 'code' && this.addressInfo.can_modify_addr) { // 扫地址码点餐，可以修改地址的时候走这里
      	this.SET_SELECT({
      	  key: 'address_info',
      	  data: {
      			type: 'code', // type用于区分扫码或者非扫码
      			addr_center_id: this.defaultAddressValue[this.defaultAddressValue.length - 1],
      			full_addr_name: e.full_addr_name,
      			can_modify_addr: this.addressInfo.can_modify_addr,
            company_id: this.addressInfo.company_id // 加多个公司id用于游客免登
      		},
      	})
      } else if (this.addressInfo.type !== 'code' && this.hallCheckedValue === 'waimai') { // 普通外卖选地址信息
      	this.SET_SELECT({
      		key: 'address_info',
      		data: {
      			type: 'nocode', // type用于区分扫码或者非扫码
      			addr_center_id: this.defaultAddressValue[this.defaultAddressValue.length - 1],
      			full_addr_name: e.full_addr_name
      		}
      	})
      }
		},
    // 游客获取公众号配置，去跳授权
    async getWechatValidate() {
      await apiGetWechatValidate({
        company_id: this.codeInfo.codeCompanyId
      })
        .then(res => {
          if (res.code == 0) {
            uni.setStorageSync('appidVisitor', res.data.appid)
            let redirectUrl = encodeQuery(`${res.data.redirect_uri}/pages_bundle/select/select_diner`)
            window.location.href = `https://open.weixin.qq.com/connect/oauth2/authorize?appid=${res.data.appid}&redirect_uri=${redirectUrl}&response_type=code&scope=snsapi_base&state=snsapi_base_get_openid#wechat_redirect`
          } else {
            uni.$u.toast(res.msg)
          }
        })
        .catch(err => {
          uni.$u.toast(err)
        })
    },
    // 游客获取openid
    async getSnsapiBaseOpenid(code) {
      await apiGetUserOpenid({
        code,
        appid: uni.getStorageSync('appidVisitor'),
        company_id: this.codeInfo.codeCompanyId
      })
        .then(res => {
          if (res.code == 0) {
            this.setAddressVisitor(1)
            uni.setStorageSync('companyId', this.codeInfo.codeCompanyId)
            uni.setStorageSync('codeOpenid', res.data.openid)
          } else {
            uni.$u.toast(res.msg)
          }
        })
        .catch(err => {
          uni.$u.toast(err)
        })
    },
    //  更新页面数据
    updateView() {
      // 赋值就餐人
      var name = this.isAddressVisitor ? "游客" : this.reservationInfo.name
      var personNo = this.isAddressVisitor ? 'visitor' : this.reservationInfo.person_no
      var groups = this.reservationInfo.groups || []
      var userListData = this.reservationInfo.user_list_data || {}
      console.log("this.$store.state.appoint.select", this.$store.state.appoint.select);
      if (!personNo && Reflect.has(userListData, 'data') && Array.isArray(userListData.data) && userListData.data.length >= 1) {
        this.personnelList = deepClone(userListData.data)
        // 如果有默认值的情况下要进行赋值
        if (this.$store.state.appoint.select.person.person_no) {
          const personData = this.personnelList.find(item => item.person_no === this.$store.state.appoint.select.person.person_no)
          if (personData) {
            this.personnelCheckedValue = personData.person_no
            this.personNoSelect = personData
          }
        } else {
          this.personnelCheckedValue = userListData.data[0].person_no
          this.personNoSelect = userListData.data[0]
        }
        this.getBookingUserGetCanteenList(this.personnelList[0])
      } else {
        this.personnelList = [{ name: name, person_no: personNo, groups: groups }]
        this.personnelCheckedValue = personNo
        this.personNoSelect = this.personnelList[0]
      }
      this.SET_SELECT({
        key: 'person',
        data: this.personNoSelect
      })
      // 赋值组织
      var orgId = this.codeType === "address" ? this.relatedOrg.org_id : this.reservationInfo.org_id
      var orgName = this.codeType === "address" ? this.relatedOrg.org_name : this.reservationInfo.org_name
      var canteenListData = this.reservationInfo.canteen_list_data || {}
      var orgSelect = {}
      if (!orgId && Reflect.has(canteenListData, 'data') && Array.isArray(canteenListData.data) && canteenListData.data.length >= 1) {
        this.orgDataList = deepClone(canteenListData.data)
        // 如果有默认值
        if (this.$store.state.appoint.select.org.org_id) {
          const orgData = this.orgDataList.find(item => item.org_id === this.$store.state.appoint.select.org.org_id)
          if (orgData) {
            this.orgCheckedValue = orgData.org_id
            orgSelect = orgData
          }
        } else {
          this.orgCheckedValue = canteenListData.data[0].org_id
          orgSelect = canteenListData.data[0]
        }
      } else if (orgId) {
        this.orgDataList = [{ org_id: orgId, org_name: orgName }]
        this.orgCheckedValue = orgId
        orgSelect = this.orgDataList[0]
      }
      this.SET_SELECT({
        key: 'org',
        data: orgSelect || {}
      })
      console.log("this.orgCheckedValue", this.orgCheckedValue);
      // 赋值就餐方式
      var takeMealTypeData = this.reservationInfo.take_meal_type_data || []
      if (takeMealTypeData && Array.isArray(takeMealTypeData) && takeMealTypeData.length > 0) {
        this.selectHallFood = deepClone(takeMealTypeData)
        if (this.codeType === "address") {
          if (this.selectHallFood.find(item => item.take_meal === 'waimai')) {
            this.hallCheckedValue = 'waimai'
            if (this.addressInfo.can_modify_addr) {  // 扫码允许修改地址
              this.showToSelectAddress = true
            } else {
              this.showAddressName = true
            }
            this.SET_SELECT({
              key: 'take_meal_type',
              data: this.hallCheckedValue
            })
          } else { // 如果没有外卖选项
            uni.$u.toast('当前项目点暂未开通外卖地址码点餐')
          }
          // 有默认的话给默认
        } else if (this.$store.state.appoint.select.take_meal_type) {
          // 找到旧的赋值，没有的取消赋值
          const selectHallFoodFilterList = takeMealTypeData.filter(
            item => item.take_meal == this.$store.state.appoint.select.take_meal_type
          )
          if (selectHallFoodFilterList.length) {
            this.hallCheckedValue = selectHallFoodFilterList[0].take_meal
            this.SET_SELECT({
              key: 'take_meal_type',
              data: this.hallCheckedValue
            })
            console.log("this.hallCheckedValue", this.hallCheckedValue);
            if (!this.addressInfo.can_modify_addr && this.addressInfo.type === 'code') {
              this.showAddressName = true
            } else if (this.hallCheckedValue === 'waimai') { // 如果是外卖，或者从点餐页再次进入当前页，且扫码支持修改地址的，需要显示地址选择
              this.showToSelectAddress = true
              this.getPreAddress()
            }
          } else {
            this.hallCheckedValue = ''
            this.SET_SELECT({
              key: 'take_meal_type',
              data: ''
            })
          }
        } else {
          this.hallCheckedValue = ''
          this.SET_SELECT({
            key: 'take_meal_type',
            data: ''
          })
        }
      } else if (this.orgCheckedValue) {
        // 如果是有默认选择食堂，需要手动调用获取就餐方式，因为多组织后台这个接口没有返回就餐方式的
        this.getTakeMealTypeList(this.personNoSelect, this.orgCheckedValue)
      }
    },
    // 获取预约点餐信息，三个接口合并一个
    async getBookingUserGetUserOrderSetting() {
    
      var params = {
        is_visitor: this.isAddressVisitor,
        h5_type: this.type === 'report_meal' ? 'report_meal' : this.type,
        company_id: this.isAddressVisitor ? this.codeCompanyId :  this.userinfo.company_id
      }
      if (this.isAddressVisitor) {
       params.user_id = '1663815737gd3qlqynvsoz3qjng666'
      }
      this.$showLoading({
        title: '加载中...',
        mask: true
      })
      const [err, res] = await this.$to(apiBookingUserGetUserOrderSetting(params))
      uni.hideLoading()
      console.log("getBookingUserGetUserOrderSetting", res);
      if (err) {
        return this.$u.toast(err.message || '获取点餐信息失败，请重新扫码')
      }
      if (res && res.code === 0) {
        console.log("getBookingUserGetUserOrderSetting", res);
        var data = res.data || {}
        if (data && typeof data === 'object') {
          console.log('data111', data, typeof data)
          this.reservationInfo = deepClone(data)
          this.updateView()
          }else {
          this.$u.toast(res.msg || '获取点餐信息失败，请重新扫码')
          }
        } else {
          this.$u.toast(res.msg || '获取点餐信息失败，请重新扫码')
        }
    },
  },
  onShow() {
    this.floatingPopupShow = !this.floatingPopupShow
  },
  async onLoad(option) {
    this.userinfo = Cache.get('userInfo')
    var appointType = this.$Route.query.appointType ? this.$Route.query.appointType : ''
    if (this.$Route.query.type) {
      this.type = this.$Route.query.type
			this.SET_SELECT({ // 直接扫码的时候可能是没存payment_order_type的，直接在这里存下吧
			  key: 'payment_order_type',
			  data: this.type
			})
    }
    // #ifdef H5
    if (option.state === 'snsapi_base_get_openid' && option.code) {
      // console.log(option.code)
      await this.getSnsapiBaseOpenid(option.code)
    }
    // 先判断类型是否是预约点餐， 如果是预约点餐，需要获取缓存数据然后展示在页面上，减少接口请求
    if(this.type === 'reservation' && appointType === 'appoint'){
      this.reservationInfo = Cache.get('reservationInfo') ?   Cache.get('reservationInfo') : {}
      this.updateView()
    } else if (this.$Route.query.code_type === "address") { // 直接扫地址码进来的
      this.isCodeDisabled = true // 扫码，不能修改组织、取餐方式等信息，禁用掉
      // 要去除保存的项目点的数据,当前是为了兼容扫码点餐
      Cache.set('projectListCompanyId', this.$Route.query.company_id, 86400)
      // 尝试下修复部分机型读取不到codeInfo的数据
      await this.setCodeInfo({
        codeNo: Number(this.$Route.query.code_no),
        codeType: this.$Route.query.code_type,
        codeCompanyId: Number(this.$Route.query.company_id),
        type: this.$Route.query.type
      })
			// uni.setStorageSync('codeInfo', {
      //   codeNo: this.$Route.query.code_no,
      //   codeType: this.$Route.query.code_type,
      //   codeCompanyId: this.$Route.query.company_id,
      //   type: this.$Route.query.type
      // })
      this.codeNo = this.$Route.query.code_no
      this.codeType = this.$Route.query.code_type
      this.codeCompanyId = Number(this.$Route.query.company_id)
      if (this.isAddressVisitor) { // 之前已经是游客状态了，继续扫码游客点餐
      // alert(`${this.codeCompanyId}~${this.addressInfo.company_id}`)
        if (this.codeCompanyId === this.addressInfo.company_id) { // 是同一个组织
          await this.getVisitorSetting() // 先校验一下还能否继续游客
          if (this.allowVisitorlogin) {
            this.getAddressDetail(this.codeCompanyId) // 游客直接获取地址详情
          } else {
            this.$miRouter.replace({
              path: '/pages/login/login'
            })
          }
        } else { // 不同组织或者没有地址信息也会走（未登录过）
          this.$miRouter.replace({
            path: '/pages/login/login'
          })
        }
      } else {
        this.gotoCheckCompany() // 非游客要去校验项目点
      }
    } else if (this.codeInfo) { // 扫码进来没登陆，跳去登录回来，拿下缓存，继续点餐
      console.log("222222", this.codeInfo);
      this.codeNo = Number(this.codeInfo.codeNo)
      this.codeType = this.codeInfo.codeType
      this.codeCompanyId = Number(this.codeInfo.codeCompanyId)
      this.type = this.codeInfo.type
      this.SET_SELECT({ // 从登录页回来是没存payment_order_type的，直接在这里存下吧
        key: 'payment_order_type',
        data: this.type
      })
      if (this.isAddressVisitor) { // 走免登录
        this.getAddressDetail(this.codeCompanyId) // 游客直接获取地址详情
      } else {
        this.gotoCheckCompany() // 非游客要去校验项目点
      }
    } else { // 非扫码，或者扫码点餐了，然后从点餐页返回到当前页，都走这里
      console.log("非扫码",this.addressInfo);
      if (this.isAddressVisitor) { // 游客需要公司id获取信息
        this.codeCompanyId = this.addressInfo.company_id
      }
      this.getBookingUserGetCardUserList()
    }
    // #endif
    // 先做好h5的扫码吧，小程序和支付宝再做兼容
    // #ifndef H5
      this.getBookingUserGetCardUserList()
    // #endif
  }
}
</script>

<style lang="scss">
.select-diner {
  position: relative;
  min-height: 100vh;
  background-image: $bg-linear-gradient-2;
  background-size: 750rpx 308rpx;
  background-repeat: no-repeat;

  .choice-title {
    padding: 45rpx 0 40rpx 70rpx;
    font-size: 36rpx;
    // font-weight: bold;
    color: #333333;
    line-height: 36rpx;
  }

  .select-diner-item {
    margin: 0 40rpx 20rpx 40rpx;
    border-radius: 20rpx;
    padding: 0 30rpx;
    background: #ffffff;
  }

  .diner-determine {
    width: 100%;
    // position: fixed;
    // bottom: 0;
    // left: 0;
    padding: 0 40rpx;
    padding-bottom: calc(54rpx + env(safe-area-inset-bottom));
  }
	
	.address-name{
    margin: 20rpx 50rpx 40rpx 50rpx;
		display: flex;
		justify-content: space-between;
	}
	
	.select-address{
		margin: 0 40rpx 20rpx 40rpx;
		padding: 40rpx;
		border-radius: 20rpx;
	}
	
	.confirm-modal{
    position: relative;
    .modal-close-btn{
      position: absolute;
      top: 8rpx;
      right: 8rpx;
    }
  }
}
</style>
