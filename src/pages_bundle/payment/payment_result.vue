<template>
	<!-- 支付结果页面 -->
	<view :style="theme.style" class="payment-result">
		<view v-if="progressStatus === 2" class="payment-success">
			<view class="flex flex-col flex-center">
				<u-icon color="#11E69E" name="checkmark-circle-fill" :size="200"></u-icon>
				<view class="font-size-42 f-w-500">支付成功</view>
				<price-format size="66" weight="600" :price="resultInfo.pay_total_fee"></price-format>
			</view>
			<view class="payment-info">
				<view class="info-item">
					<view class="muted">就餐人：</view>
					<view>{{ resultInfo.name }}</view>
				</view>
				<view class="info-item" v-if="isShowDetail">
					<view class="muted">订单原金额：</view>
          <!-- <view>￥{{getOrderOriginFee()}}</view> -->
					<price-format :price="getOrderOriginFee()"></price-format>
        </view>
				<view class="info-item" v-if="isShowDetail">
					<view class="muted">支付总金额：</view>
					<price-format :price="resultInfo.pay_total_fee"></price-format>
				</view>
				<view class="info-item" v-if="resultInfo.discount_total_fee && isShowDetail">
					<view class="muted">优惠金额：</view>
					<view>
						<span class="red">-</span>
						<price-format :price="resultInfo.discount_total_fee" color="red"></price-format>
					</view>
					<!-- <view class="red">-￥{{ resultInfo.discount_total_fee ? resultInfo.discount_total_fee / 100 : 0 }}</view> -->
				</view>
				<view class="info-item">
					<view class="muted">下单时间：</view>
					<view>{{ resultInfo.pay_time }}</view>
				</view>
				<view class="info-item" v-if="resultInfo.rate_fee && isShowDetail">
					<view class="muted">扣款手续费：</view>
					<price-format :price="resultInfo.rate_fee"></price-format>
				</view>
				<view class="info-item" v-if="isShowDetail">
					<view class="muted">线上支付金额：</view>
					<price-format :price="resultInfo.online_fee"></price-format>
				</view>
				<view class="info-item" v-if="isShowDetail">
          <view class="muted">线下支付金额：</view>
					<price-format :price="resultInfo.offline_fee"></price-format>
        </view>
				<view class="info-item" @click="gotoOrder">
					<view class="muted">总订单号：</view>
					<view class="flex flex-center">
						{{ resultInfo.trade_no }}
						<u-icon color="#999" name="arrow-right" :size="28"></u-icon>
					</view>
				</view>
				<!-- 
        <view class="info-item">
          <view class="muted">线下支付订单号：</view>
          <view class="flex">12345679898798 <u-icon color="#999" name="arrow-right" :size="28"></u-icon></view>
        </view> -->
			</view>
			<!-- <router-link to="/pages_bundle/appoint/user_appoint">
        <u-button shape="circle" color="linear-gradient(90deg, #A9FED5 0%, #11E69E 0%, #11E69E 0%, #11E6C5 100%, #11E6C5 100%)" text="确定"></u-button>
      </router-link> -->
			<view class="" @click="gotoOrder">
				<u-button
					shape="circle"
					:color="variables.bgLinearGradient1"
					text="确定"
				></u-button>
			</view>
			<!-- 推广图 -->
			<view class="p-t-20">
				<generalization-map v-if="isGetGeneralizationMapShow&&hasGeneralizationMap" ref="generalizationMap" uiType="reservation" :key="refresh" class="m-t-20 m-b-20" @transfer="getList" :pageType="'index'"></generalization-map>
			</view>

		</view>
		<view v-else-if="progressStatus === 1" class="payment-fail m-t-50">
			<view class="flex flex-col flex-center">
				<u-icon color="#11E69E" name="clock-fill" :size="150"></u-icon>
				<!-- <image color="#11E69E" name="@/static/icons/canteen_more.png" :size="150"></image> -->
				<!-- <image style="width: 150rpx; height: 150rpx;" :src="$imgPath.img_icon_my_fuwu_notice"></image> -->
				<view class="font-size-38 f-w-500 m-t-30">查询中</view>
			</view>
		</view>
		<view v-else-if="progressStatus === 3" class="payment-fail m-t-50">
			<view class="flex flex-col flex-center">
				<u-icon color="red" name="close-circle" :size="150"></u-icon>
				<view class="font-size-38 f-w-100 red m-t-30">{{ progressText }}</view>
			</view>
			<view class="flex flex-center m-t-50">
				<view class="m-r-20" style="width: 30%;" @click="gotoIndex"><u-button plain text="返回首页"></u-button></view>
				<view class="m-l-20" style="width: 30%;" @click="gotoOrder">
					<u-button
						:color="variables.bgLinearGradient1"
						text="查看订单"
					></u-button>
				</view>
			</view>
		</view>
		
		<!-- 满意度 -->
    <image v-if="isShowStatisfaction" :src="themeImgPath.img_banner_idea_big" style="width: 100%;height: 200rpx;display: block;margin: 0 auto; margin-top: 68rpx;" @click="onEditorSatisfaction" />
    <!-- 评价弹窗 -->
    <statisfaction-modal ref="statisfactionMoadlRef" :tips="statisfactionTips" :tags="tags" :satisfactionForm.sync="satisfactionForm" :visible.sync="sModalVisible" @onModalSuccess="onModalSuccess" />
	</view>
</template>

<script>
import { queryOrderInfo } from '@/api/user.js'
import { plus, getCurrentQuarter } from '@/utils/util.js'
import generalizationMap from '@//components/generalization-map/generalization-map'
import { mapGetters } from 'vuex'
import Cache from '@/utils/cache'
import StatisfactionModal from '@/components/statisfaction-modal/statisfaction-modal.vue'
import appMixins from '@/mixins/app.js'
import {setStorage, getStorage} from '@/utils/storage'
export default {
	mixins: [appMixins],
	components: {generalizationMap, StatisfactionModal},
	data() {
		return {
			// 满意度
			statisfactionTips: '',
			tags: [],
			isShowStatisfaction: false,
			sModalVisible: false,
			satisfactionForm: {
				is_satisfied: true,
				reason: '',
				module_key: '' // report: '报餐', reservation: '预约'
			},
			isGetGeneralizationMapShow: false,
			hasGeneralizationMap: true,
			resultInfo: {},
			paymentOrderType: '',
			tradeNo: '', // 订单号
			progressStatus: 1, // 1查询中，2成功，3失败
			progressText: '查询失败，请联系客服',
			queryHandle: null, // 定时查询订单结果任务
			isShowDetail: false,  // 是否显示具体的信息，20231220张华杰要求把其他信息都去掉，只留就餐人，下单时间，总订单好
		}
	},
	computed: {
		...mapGetters(['isAddressVisitor'])
	},
	methods: {
		 // 提交评价回调
		onModalSuccess() {
      console.log('onModalSuccess----');
    },
    onEditorSatisfaction() {
      this.sModalVisible = true
    },
		getList(e) {
			console.log('e', e)
      if (e && e.length !== 0) {
        this.hasGeneralizationMap = true
      } else {
        this.hasGeneralizationMap = false
      }
    },
		queryResult() {
			this.$showLoading({
				title: '查询中....',
				mask: true
			})
			this.queryHandle = setInterval(() => {
				this.queryPayResult()
				// clearInterval(this.queryHandle);
			}, 1000)
		},
		// 查询支付结果
		queryPayResult() {
			if (this.tradeNo) {
				queryOrderInfo({
					trade_no: this.tradeNo
				})
					.then(res => {
						if (res.code === 0) {
							if (res.data.order_status === 'ORDER_SUCCESS') {
								this.progressStatus = 2
								this.resultInfo = res.data
								uni.hideLoading()
								if (this.queryHandle) {
									clearInterval(this.queryHandle)
								}
							}
						} else {
							this.progressStatus = 3
							uni.hideLoading()
							if (this.queryHandle) {
								clearInterval(this.queryHandle)
							}
							this.progressText = res.msg
							uni.$u.toast(res.msg)
						}
					})
					.catch(err => {
						this.progressStatus = 3
						uni.hideLoading()
						if (this.queryHandle) {
							clearInterval(this.queryHandle)
						}
					})
			} else {
				uni.hideLoading()
				uni.$u.toast('订单号不能为空！')
				if (this.queryHandle) {
					clearInterval(this.queryHandle)
				}
			}
		},
		gotoOrder() {
			let path
			if (this.isAddressVisitor) {
				this.$miRouter.replaceAll({
				  path: '/pages/user/user'
				})
			} else {
				if (this.paymentOrderType === 'reservation') {
					path = '/pages_bundle/appoint/user_appoint'
				} else if (this.paymentOrderType === 'report_meal') {
					path = '/pages_bundle/meal_report/user_meal_report'
				}
				this.$miRouter.replace({
					path
				})
			}
			
		},
		gotoIndex() {
			this.$miRouter.replace({
				path: '/pages/index/index'
			})
		},
		plus,
    getOrderOriginFee() {
      if (this.resultInfo.discount_total_fee) {
        return this.plus(this.resultInfo.pay_total_fee, this.resultInfo.discount_total_fee)
      } else {
        return this.resultInfo.pay_total_fee
      }
    }
	},
	onShow() {},
	onLoad(options) {
		this.paymentOrderType = this.$store.state.appoint.select.payment_order_type
		console.log('测试type', this.paymentOrderType)
		
		// 传值弹窗 订单类型
		this.satisfactionForm.module_key = this.$store.state.appoint.select.payment_order_type
		this.$nextTick(async () => {
			// 判断是否显示评价弹窗 
			const moduleKey = this.satisfactionForm.module_key === 'report_meal' ? "report" : this.satisfactionForm.module_key // key
			const today = uni.$u.timeFormat(new Date().getTime(), 'yyyy-mm-dd') // 获取当天时间
			const isModuleDay = getStorage(moduleKey) // 获取当天弹窗时间
			const result = await this._appGetSatisfaction(moduleKey) // 获取记录

			if (!result) return

			const isEveryDay = result.is_every_day
			const storageKey = isEveryDay ? moduleKey : `${moduleKey}_quarter`
			const lastTime = getStorage(storageKey)
			const currentTime = isEveryDay ? today : getCurrentQuarter()
			if (lastTime && lastTime === currentTime) {
				this.sModalVisible = false
			} else {
				setStorage({ name: moduleKey, content: today }) // 记录当天弹窗时间
				if (!isEveryDay) {
					setStorage({ name: `${moduleKey}_quarter`, content: getCurrentQuarter() }) // 记录当前季度
				}
				this.sModalVisible = true
			}
			// if(isModuleDay && isModuleDay === today) { // 一天内只弹一次
			// 	this.sModalVisible = false
			// } else { // 主动弹窗
			// 	setStorage({name: moduleKey, content:today}) // 记录当天弹窗时间
			// 	this.sModalVisible = true
			// }
			this.isShowStatisfaction = true
			this.statisfactionTips = result.tips
			this.tags = result.tags
			
			this.satisfactionForm.module_key = result.module_key
			// this.satisfactionForm.is_satisfied = result.is_satisfied
			this.satisfactionForm.reason = result.reason
		})
		
		if (this.$Route.query.statusType === 'success') {
			this.resultInfo = {
				name: this.$Route.query.name,
				pay_total_fee: this.$Route.query.pay_total_fee,
				online_fee: this.$Route.query.online_fee,
				offline_fee: this.$Route.query.offline_fee,
				trade_no: this.$Route.query.trade_no,
				pay_time: this.$Route.query.pay_time
			}
			this.progressStatus = 2
		} else if (this.$Route.query.statusType === 'error') {
			this.progressText = this.$Route.query.statusMsg
			this.progressStatus = 3
		} else {
			// 支付宝h5支付有out_trade_no和trade_no，先判断out_trade_no
			this.tradeNo = this.$Route.query.out_trade_no || this.$Route.query.trade_no
			// this.resultInfo = this.$Route.query.data
			if (this.tradeNo) {
				// 当有订单号时使用定时器查询充值订单
				this.queryResult()
			}
			this.progressStatus = 1
		}
		// 在此判断是否显示轮播图
		if (Cache.get('isVIP')) {
			this.isGetGeneralizationMapShow = true
		}
	},
	onUnload() {
		if (this.queryHandle) {
			clearInterval(this.queryHandle)
		}
	}
}
</script>

<style lang="scss">
page {
	background-color: #ffffff;
}

.payment-result {
	padding: 20rpx 70rpx;
	.payment-info {
		margin-bottom: 30rpx;
		.info-item {
			display: flex;
			justify-content: space-between;
			font-size: $font-size-md;
			padding: 30rpx 0;
			&:not(:last-of-type) {
				border-bottom: $border-base;
			}
		}
	}
}
</style>
