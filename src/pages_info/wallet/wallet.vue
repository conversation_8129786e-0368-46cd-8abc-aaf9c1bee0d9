<template>
	<view :style="theme.style" class="canteen-wallet">
		<!-- 总览 -->
		<view class="total-balance bg-white text-center">
			<!-- 总余额 -->
			<view class="xxs p-t-20 total-balance-title flex flex-center">总余额</view>
			<view class="f-w-500 font-size-54 m-t-34 m-b-48 f-w-500">￥{{ total }}</view>
			<!-- 统计分析&账单 -->
			<view class="wallet-btn flex row-center xs">
				<view class="m-r-40" @click="toStatisticsFunc()">
					<u-button
						type="primary"
						size="small"
						text="统计分析"
						:customStyle="customBtnStyle"
						color="linear-gradient(87deg, #FFDC63 0%, #FFDC63 0%, #FFB82F 100%, #FFB82F 100%)"
					></u-button>
				</view>
				<view @click="tobillFunc()">
					<u-button
						type="primary"
						size="small"
						text="账单"
						:customStyle="customBtnStyle"
						:color="variables.colorPrimaryLight1"
					></u-button>
				</view>
			</view>

			<!-- 分类 -->
			<view class="classification flex flex-1 flex-center row-around">
				<view class="classification-item">
					<view class="m-b-5 nr f-w-500">￥{{ totalBalance }}</view>
					<view class="xxs">储值类</view>
				</view>
				<view class="classification-item">
					<view class="m-b-5 nr f-w-500">￥{{ complimentaryBalance }}</view>
					<view class="xxs">赠送类</view>
				</view>
				<view class="classification-item">
					<view class="m-b-5 nr f-w-500">￥{{ subsidyBalance }}</view>
					<view class="xxs">补贴类</view>
				</view>
			</view>
		</view>

    <!-- 某一食堂钱包 -->
    <view v-for="(item, index) in walletDataList" :key="index">
      <view class="canteen-all">
        <view class="canteen-title nr f-w-500">
          {{ item.name }}
          <text class="muted">{{ personNo ? '' : '（'+item.card_name+'）'}}</text>
        </view>
        <view class="canteen bg-white sm">
          <!-- @click="toWalletFunc" -->
          <view class="canteen-item flex flex-center row-between" v-for="(welletItem, windex) in item.wallet_list" :key="windex" @click="toWalletFunc(item, welletItem)">
            <view class="flex flex-center">
              <view class="">
                <u-image
                  width="54rpx"
                  height="54rpx"
                  v-if="welletItem.balance_type == 'store'"
                  :src="themeImgPath.img_bundle_qianbao_chuzhi"
                ></u-image>
                <u-image
                  width="54rpx"
                  height="54rpx"
                  v-else-if="welletItem.balance_type == 'complimentary'"
                  :src="themeImgPath.img_bundle_qianbao_zengsong"
                ></u-image>
                <u-image
                  width="54rpx"
                  height="54rpx"
                  v-else-if="welletItem.balance_type == 'subsidy'"
                  :src="themeImgPath.img_bundle_qianbao_butie"
                ></u-image>
              </view>
              <view class="m-l-40 xs f-w-500">
                {{ welletItem.name }}
              </view>
            </view>
            <view class="flex flex-center">
              <view class="m-r-30 md f-w-500">￥{{ (welletItem.blance / 100).toFixed(2) }}</view>
              <u-icon v-if="welletItem.balance_type == 'store'" color="inherit" name="arrow-right" size="24rpx"></u-icon>
            </view>
          </view>
        </view>
				<view class="" v-for="(subsidyitem, subsidyindex) in unreceivedSubsidyList" :key="subsidyindex">
					<view class="" v-if="subsidyitem.org_id === item.source_organization_id">
						<view class="subsidy-tips">
							<view class="subsidy-tips-text">
								{{subsidyitem.label}}待领取补贴:{{(subsidyitem.plan_release_money/ 100).toFixed(2)}}元
							</view>
							<view class="subsidy-tips-btn" @click="checkSubsidy(subsidyitem, item.wallet_list)">领取</view>
						</view>
					</view>
				</view>
      </view>
    </view>
		<u-modal
		  :show="showModal"
		  :title="modalTitle"
		  :content="modalText"
		  width="280px"
		  :confirmColor="variables.colorPrimary"
		  :confirmText="confirmSubsidyText"
      :showCancelButton="isShowCancelButton"
		  @confirm="confirmModal"
		  @cancel="showModal = false"
		  >
			<view class="slot-content">
				<rich-text :nodes="modalText"></rich-text>
			</view>
		</u-modal>
		<!-- 支付方式 -->
    <u-modal
      :show="rechargeSettingShow"
      :title="'支付方式'"
      confirmColor="#5A6080"
      confirmText="充值"
      @confirm="confirmRechargeHandle"
      :showCancelButton="true"
      cancelText="取消"
      @cancel="cancelRechargeHandle"
    >
      <u-radio-group placement="column" v-model="radioPayinfoId" size="35" iconPlacement="right">
        <u-radio
          :customStyle="{ marginBottom: '16px' }"
          v-for="(item, index) in payinfosList"
          @change="radioRechargeChange(item)"
          :key="index"
          :activeColor="variables.colorPrimary"
          :label="item.payway_alias"
          :name="item.id"
        ></u-radio>
      </u-radio-group>
    </u-modal>
  </view>
</template>

<script>
import { getApiUserWalletList } from '@/api/user.js'
import { apiGetUserUnreceivedSubsidy, apiReceivedSubsidy } from '@/api/wallet.js'
import { getApiRechargeGetSettings, getApiRechargeOrderCreate } from '@/api/user.js'
import { payMpRequest, checkWxBridgeReady, setWxJssdkConfig, payWxJssdkRequest } from '@/utils/payment'
import Cache from '@/utils/cache'
import { divide, times, debounce } from '@/utils/util.js'

export default {
  data() {
    return {
      imgPath: this.$imgPath,
      totalBalance: 0,
      total: 0,
      subsidyBalance: 0,
      complimentaryBalance: 0,
      walletDataList: [],
      companyId: '',
      companyName: '',
      customBtnStyle: {
        minWidth: '120rpx',
        height: '60rpx',
        lineHeight: '60rpx',
      },
      personNo: '', // 人员编号， 用于区分用户是从首页进还是钱包中心进，多用户适配
			unreceivedSubsidyList: [],
			subsidyInfo: {},
			isShowCancelButton: true,
			showModal: false,
			modalTitle: '',
			modalText: '',
			modalType: '',
			rechargeSettingShow: false, // 是否显示充值方式
			radioPayinfoId: '', // 选中的支付方式id
			payinfosList: [], // 支付方式列表
			isLoading: false,
			rechargeMoney: 0, // 充值的金额
      confirmSubsidyText: "确定",
      tradeNo: ''
    }
  },
  onLoad() {},
  onShow() {
    if (this.$Route.query.companyId) {
    	this.companyId = this.$Route.query.companyId
    	this.companyName = this.$Route.query.companyName
    } else {
    	this.companyId = Cache.get('userInfo').company_id
    	this.companyName = Cache.get('userInfo').company_name
    }
    this.personNo = this.$Route.query.person_no
    uni.setNavigationBarTitle({
    	title: this.companyName
    })
    this.getUserWalletList()
		if (this.$Route.query.person_no) {
		  this.getUserSubsidyList() // 获取补贴
		}
  },
  methods: {
    getUserWalletList(show = false) {
      this.$showLoading({
        title: '获取中....',
        mask: true
      })
      let params = {
        company_id: this.companyId
      }
      if (this.personNo) {
        params.person_no = this.personNo
      }
      getApiUserWalletList(params)
        .then(res => {
          uni.hideLoading()
          if (res.code == 0) {
            this.totalBalance = (res.data.total_balance / 100).toFixed(2) //总余额
            this.total = (res.data.total / 100).toFixed(2) //储值类
            this.subsidyBalance = (res.data.total_subsidy_balance / 100).toFixed(2) //补贴类
            this.complimentaryBalance = (res.data.total_complimentary_balance / 100).toFixed(2) //赠送类
            this.walletDataList = res.data.wallet_list
						if (show) {
							let walletInfo = this.walletDataList.filter(item => item.source_organization_id === this.subsidyInfo.org_id)[0]
							let orgName = walletInfo.name
              let blance = walletInfo.wallet_list.find(item => item.balance_type === "subsidy").blance
							this.showModal = true
              this.isShowCancelButton = false
							this.modalType = 'success'
              this.modalTitle = '提示'
							this.modalText = `补贴领取成功，当前您在${orgName}的补贴账户余额为${(blance / 100).toFixed(2)}元`
						}
          } else {
            uni.$u.toast(res.msg)
          }
        })
        .catch(err => {
          uni.$u.toast(err)
        })
    },
		// 获取补贴列表
		getUserSubsidyList() {
			apiGetUserUnreceivedSubsidy({
				person_no: this.personNo,
				company_id:  this.companyId
			})
				.then(res => {
					if (res.code == 0) {
            this.unreceivedSubsidyList = res.data.map(item => {
                let year = item.release_date.split('-')[0]
                let month = item.release_date.split('-')[1]
              if (item.subsidy_type === 'ONE_RELEASE') {
                item.label = ''
              } else if (item.subsidy_type === 'DAY_RELEASE') {
                item.label = item.release_date
              } else if (item.subsidy_type === 'WEEK_RELEASE') {
                item.label = year + '年第' + item.release_week + '周'
              } else if (item.subsidy_type === 'MONTH_RELEASE') {
                item.label = year + '年' + month + '月'
              }
              return item
            })
					} else {
					  uni.$u.toast(res.msg)
					}
				})
				.catch(err => {
					console.log(err);
				});
		},
		checkSubsidy(info, walletList) {
      console.log(11, info, walletList)
      // 充值补贴支付中或支付成功的状态不需要往下走，等待后台回调成功刷新数据即可
      if (info.receive_way === 'TOPUP' && (info.charge_status === 'ORDER_PAYING' || info.charge_status === 'ORDER_SUCCESS')) {
        setTimeout(() => {
          this.getUserSubsidyList()
				  this.getUserWalletList()
        }, 1000);
        uni.$u.toast('处理中，请刷新！')
        return
      }
      this.confirmSubsidyText = "确定"
			this.subsidyInfo = info
      if (this.subsidyInfo.max_subsidy_balance > 0) {
        let subsidyBlance = walletList.find(item => item.balance_type === "subsidy").blance
        let canReceiveBalance = this.subsidyInfo.max_subsidy_balance - subsidyBlance
        if (canReceiveBalance > 0) { // 可领取
          if (info.receive_way === 'TOPUP') {
            this.confirmSubsidyText = "充值领取"
          }
          if (canReceiveBalance >= this.subsidyInfo.plan_release_money) { // 全部领取
            if (info.receive_way === 'TOPUP') {
              this.showModal = true
              this.isShowCancelButton = true
              this.modalType = 'allReceive'
              this.modalTitle = '温馨提示'
              this.modalText = `充值${divide(info.top_up_money)}元即可领取${divide(info.subsidy_money)}元补贴<br />`
              if (info.settings?.is_top_up_money_refund==1) {
                this.addRefundTxt(info)
              }
            } else {
              this.showModal = true
              this.isShowCancelButton = true
              this.modalType = 'allReceive'
              this.modalTitle = '温馨提示'
              this.modalText = `是否领取该补贴？`
            }
          } else { // 部分领取
            if (info.receive_way === 'TOPUP') {
              this.showModal = true
              this.isShowCancelButton = true
              this.modalType = 'allReceive'
              this.modalTitle = '温馨提示'
              this.modalText = `充值${divide(info.top_up_money)}元即可领取${divide(info.subsidy_money)}元补贴<br />`
              if (info.settings?.is_top_up_money_refund==1) {
                this.addRefundTxt(info)
              }
              this.modalText += `因您的补贴上限为：${(this.subsidyInfo.max_subsidy_balance / 100).toFixed(2)}元<br />
                                故您能领取的补贴为：${(canReceiveBalance / 100).toFixed(2)}元<br />
                                超出金额${((this.subsidyInfo.plan_release_money - canReceiveBalance) / 100).toFixed(2)}元作废，是否继续领取补贴？`
            } else {
              this.showModal = true
              this.isShowCancelButton = true
              this.modalType = 'partReceive' // 只能领取部分
              this.modalTitle = '温馨提示'
              this.modalText = `因您的补贴上限为：${(this.subsidyInfo.max_subsidy_balance / 100).toFixed(2)}元<br />
                                故您能领取的补贴为：${(canReceiveBalance / 100).toFixed(2)}元<br />
                                超出金额${((this.subsidyInfo.plan_release_money - canReceiveBalance) / 100).toFixed(2)}元作废，是否继续领取补贴？`
            }
          	
          }
        } else if(canReceiveBalance <= 0) { // 领取不了
          this.showModal = true
          this.isShowCancelButton = false
          this.modalType = 'fail'
          this.modalTitle = '温馨提示'
          this.modalText = `您的补贴余额上限为：${(this.subsidyInfo.max_subsidy_balance / 100).toFixed(2)}元<br />
          									补贴钱包余额已达到：${(subsidyBlance / 100).toFixed(2)}元<br />
          									无法领取该补贴`
        }
      } else if (this.subsidyInfo.max_subsidy_balance === null){ // 无限制最大金额，直接领取
        if (info.receive_way === 'TOPUP') {
          this.confirmSubsidyText = "充值领取"
          this.showModal = true
          this.isShowCancelButton = true
          this.modalType = 'allReceive'
          this.modalTitle = '温馨提示'
          this.modalText = `充值${divide(info.top_up_money)}元即可领取${divide(info.subsidy_money)}元补贴<br />`
          if (info.settings?.is_top_up_money_refund==1) {
            this.addRefundTxt(info)
          }
        } else {
          this.showModal = true
          this.isShowCancelButton = true
          this.modalType = 'allReceive'
          this.modalTitle = '温馨提示'
          this.modalText = `是否领取该补贴？`
        }
      }
		},
    // 添加延迟退款信息
    addRefundTxt(info) {
      if (!info) {
        return
      }
      this.modalText += `领取成功后，${divide(info.top_up_money)}元`
      let messgageTip = Reflect.has(info, 'delay_refund') && info.delay_refund ? `充值金额将原路退回,退款可能存在延时,<span style="color:red;">请确保钱包余额充足</span>。` : '会立即原路退还。'
      this.modalText += messgageTip
    },
		confirmModal: debounce(function () {
      // 充值领取
      if (this.subsidyInfo.receive_way === 'TOPUP' && !this.subsidyInfo.is_paid) {
        this.getRechargeGetSettings()
        this.showModal = false
      } else { // 常规
        if (this.modalType === 'success') {
          this.showModal = false
        } else if (this.modalType === 'partReceive' || this.modalType === 'allReceive') {
          this.showModal = false
          this.goToReceive()
        } else if (this.modalType === 'fail') {
          this.showModal = false
        }
      }
		}, 1000),
		// 领取补贴
		goToReceive() {
			apiReceivedSubsidy({
				person_no: this.personNo,
				company_id:  this.companyId,
				subsidy_ids: [this.subsidyInfo.id],
				org_id: this.subsidyInfo.org_id
			})
				.then(res => {
					if (res.code == 0) {
            this.getUserSubsidyList()
						this.getUserWalletList(true)
					} else {
					  uni.$u.toast(res.msg)
					}
				})
				.catch(err => {
					console.log(err);
				});
		},
    // 去某一钱包
    toWalletFunc(item, data) {
      // 如果是储值钱包就跳提现
      if (data.balance_type != 'store') return
      this.$miRouter.push({
        path: '/pages_info/wallet/recharge_wallet',
        query: {
          org_id: data.org_id,
          company_id: this.companyId,
          person_no: this.personNo ? this.personNo : item.person_no
        }
      })
    },

		// 去账单
		tobillFunc() {
      Cache.set('walletBill', {
        companyId: this.companyId,
				personNo: this.personNo ? this.personNo : ''
      })
			this.$miRouter.push({
				path: '/pages_info/wallet/bill'
			})
		},

		// 去统计
		toStatisticsFunc() {
      Cache.set('walletAnalysis', {
        companyId: this.companyId,
				personNo: this.personNo ? this.personNo : ''
      })
			this.$miRouter.push({
				path: '/pages_info/wallet/statistical_analysis'
			})
		},
		getRechargeGetSettings() {
      this.$showLoading({
        title: '获取中....',
        mask: true
      })
      let params = {
        org_id: this.subsidyInfo.org_id,
        // wallet_id: this.walletData.wallet_id
      }
      if (this.subsidyInfo.wallet_id) {
        params.wallet_id = this.subsidyInfo.wallet_id
      }
      getApiRechargeGetSettings(params)
        .then(res => {
          uni.hideLoading()
          if (res.code == 0) {
            this.rechargeSettingShow = true
            // 判断payway是否重复
            let checkKey = {}
            res.data.payinfos.map(v => {
              if (!checkKey[v.payway]) {
                checkKey[v.payway] = 1
              } else {
                checkKey[v.payway] += 1
              }
            })
            this.payinfosList = res.data.payinfos.map(v => {
              if (checkKey[v.payway] > 1) {
                v.payway_alias = v.payway_alias + '-' + v.sub_payway_alias
              }
              return v
            })
						// 给个选中的默认值
            if (this.payinfosList && this.payinfosList.length >= 1) {
              this.radioPayinfoId = this.payinfosList[0].id
            }
          } else {
            uni.$u.toast(res.msg)
          }
        })
        .catch(err => {
          uni.hideLoading()
          uni.$u.toast(err)
        })
    },
		// 选择支付方式
		radioRechargeChange(e) {
		},
		// 充值方式弹窗确认事件
		confirmRechargeHandle() {
      this.getRechargeOrderCreate()
		},
		// 充值方式弹窗取消事件
		cancelRechargeHandle() {
			this.rechargeSettingShow = false
		},
		async getRechargeOrderCreate() {
      if (!this.radioPayinfoId) {
        this.isLoading = false
        return uni.$u.toast('获取充值配置失败！请联系客服')
      }
      this.$showLoading({
        title: '充值中....',
        mask: true
      })
      let params = {
        amount: this.subsidyInfo.top_up_money,
        payinfo_id: this.radioPayinfoId,
        wallet_id: this.subsidyInfo.wallet_id,
        wallet_id: this.subsidyInfo.wallet_id,
        order_subsidy_id: this.subsidyInfo.id,
        // #ifdef H5
        return_url: window.location.href // 支付完成跳转的地址针对location.href这种方式的充值
        // #endif
      }
      getApiRechargeOrderCreate(params)
        .then(res => {
          this.tradeNo = ''
          if (res.code == 0) {
            this.rechargeSettingShow = false
            
            this.tradeNo = res.data.trade_no
             // 成功的订单，已签约免密支付的会走这一步
            if (res.data.order_status === 'ORDER_SUCCESS') {
              uni.hideLoading()
              uni.showToast({
                title: '充值成功',
                icon: 'success',
                success: () => {
                  this.isLoading = false
                  this.getUserWalletList()
                  this.getUserSubsidyList()
                }
              })
              return
            }
            if (res.data.extra && res.data.extra.redirect) {
              this.isLoading = false
              uni.hideLoading()
              window.location.href = res.data.extra.redirect
              return
            }
            // payway fastepay 农行，AliPay支付宝，WechatPay微信，sub_paypay不知道啥玩意，ShouqianbaPay收钱吧
            // sub_payway jsapi，h5，miniapp

            if (res.data.sub_payway == 'fastepay') {
              this.isLoading = false
              uni.hideLoading()
              window.location.href = res.data.extra.redirect
              // this.setAbcRechargeCodeUrl(res.data.extra.redirect)
              // if (store.getters.abcRechargeCodeUrl) this.$miRouter.push({
              // 	path: '/pages_bundle/recharge/abc_recharge_code',
              // 	query: {
              // 		tradeNo: res.data.trade_no,
              // 		type: 'ABC'
              // 	}
              // })
              return
            } else if (res.data.sub_payway == 'jsapi' || res.data.sub_payway === 'miniapp') {
              this.jsapiChooseWXPay(res.data.extra)
            } else if ((res.data.payway == 'AliPay' || res.data.payway == 'sub_paypay' || res.data.payway == 'ShouqianbaPay') && res.data.sub_payway === 'h5') { // h5走location
              this.isLoading = false
              uni.hideLoading()
              // 手动设置下订单号，用于兼容支付宝h5支付
              // Cache.set('RECHARGETRADENO', this.tradeNo)
              window.location.href = res.data.extra
            } else {
              this.isLoading = false
              uni.hideLoading()
              uni.$u.toast('暂不支持当前充值方式！等待工程师赶制中！')
            }
            // this.$miRouter.push({
            // path: '/pages_bundle/recharge/recharge_status',
            // query: {
            // 	money: this.money
            // }
            // })
            // uni.$u.toast('成功')
          } else {
            this.isLoading = false
            uni.$u.toast(res.msg)
          }
        })
        .catch(err => {
          this.isLoading = false
          uni.hideLoading()
          uni.$u.toast(err)
        })
    },
		// 拉起充值
    jsapiChooseWXPay(params) {
      let _this = this
      // #ifdef H5
      checkWxBridgeReady(params, function({res}) {
        if (res.err_msg == 'get_brand_wcpay_request:ok') {
          // 使用以上方式判断前端返回,微信团队郑重提示：
          //res.err_msg将在用户支付成功后返回ok，但并不保证它绝对可靠。
          uni.hideLoading()
          uni.showToast({
            title: '充值成功',
            icon: 'success',
            success: () => {
              this.isLoading = false
              _this.getUserWalletList()
              _this.getUserSubsidyList()
            }
          })
        } else {
          uni.hideLoading()
          uni.showToast({
            title: '充值失败',
            icon: 'none',
            success: () => {
              this.isLoading = false
            }
          })
        }
      })
      // #endif
      // #ifdef MP-WEIXIN || MP-ALIPAY
      payMpRequest(params).then(({res, provider}) => {
        uni.hideLoading()
        if (provider === 'alipay') { // 当为支付宝支付时需要额外判断状态码
          switch (res.resultCode) {
            case '9000': // 订单处理成功。
              uni.showToast({
                title: '充值成功',
                icon: 'success',
                success: () => {
                  this.isLoading = false
                  _this.getUserWalletList()
                  _this.getUserSubsidyList()
                }
              })
              break;
            case '6001': // 用户中途取消
              this.isLoading = false
              uni.showToast({
                title: '用户中途取消',
                icon: 'fail',
                success: () => {
                  this.isLoading = false
                }
              })
              break;
            case '8000': // 正在处理中。支付结果未知（有可能已经支付成功）。
              // uni.showToast({
              //   title: '正在处理中',
              //   icon: 'fail'
              // })
              if (params.trade_no) {
                // this.$miRouter.push({
                //   path: '/pages_bundle/recharge/recharge_status',
                //   query: {
                //     trade_no: params.trade_no
                //   }
                // })
                uni.showToast({
                  title: '正在处理中',
                  icon: 'success',
                  success: () => {
                    this.isLoading = false
                    _this.getUserWalletList()
                    _this.getUserSubsidyList()
                  }
                })
              } else {
                uni.showToast({
                  title: '正在处理中',
                  icon: 'fail',
                  success: () => {
                    this.isLoading = false
                  }
                })
              }
              break;
            case '6002': // 网络连接出错
              uni.showToast({
                title: '网络连接出错',
                icon: 'fail',
                success: () => {
                  this.isLoading = false
                }
              })
              break;
            case '6004': // 处理结果未知（有可能已经成功）
              // uni.showToast({
              //   title: '处理结果未知',
              //   icon: 'fail'
              // })
              uni.showToast({
                title: '正在处理中',
                icon: 'success',
                success: () => {
                  this.isLoading = false
                  _this.getUserWalletList()
                  _this.getUserSubsidyList()
                }
              })
              break;
            case '4': // 无权限调用
              uni.showToast({
                title: '无权限调用',
                icon: 'fail',
                success: () => {
                  this.isLoading = false
                }
              })
              break;
            default:
              this.isLoading = false
            break;
          }
        } else {
          uni.showToast({
            title: '充值成功',
            icon: 'success',
            success: () => {
              this.isLoading = false
              _this.getUserWalletList()
              _this.getUserSubsidyList()
            }
          })
        }
      }).catch(({res, provider}) => {
        uni.hideLoading()
        uni.showToast({
          title: '充值失败',
          icon: 'none',
          success: () => {
            this.isLoading = false
          }
        })
      })
      // #endif
    }
	}
}
</script>

<style lang="scss">
.canteen-wallet {
	padding: 20rpx 40rpx 0;

	.total-balance {
		border-radius: 20rpx;

		&-title::after {
			display: block;
			content: '';
			height: 3rpx;
			width: 60rpx;
			background: #efefef;
			margin-left: 10rpx;
		}

		&-title::before {
			display: block;
			content: '';
			height: 3rpx;
			width: 60rpx;
			background: #efefef;
			margin-right: 10rpx;
		}

		.wallet-btn {
			margin-bottom: 35rpx;
		}

		.classification {
			.classification-item {
				padding: 28rpx 0 25rpx;
			}
		}
	}

	.canteen-all {
		margin-top: 30rpx;

		.canteen-title {
			margin: 0 0 10rpx 40rpx;
		}

		.canteen {
			border-radius: 20rpx;

			.canteen-item {
				padding: 0 40rpx;
				height: 100rpx;
			}

      .canteen-item:not(:last-of-type) {
        border-bottom: 1rpx solid #efefef;
      }
    }

		.subsidy-tips{
			display: flex;
			justify-content: space-between;
			align-items: center;
			border-radius: 20rpx;
			padding: 20rpx 30rpx;
			background-color: #FFFFFF;
			margin-top: 20rpx;
			font-size: 24rpx;
			.subsidy-tips-btn{
				background-color: $color-primary;
				padding: 5rpx 30rpx;
				border-radius: 30rpx;
				color: #FFF;
			}
		}
  }
}
</style>
