<template>
  <view :style="theme.style" class="consumption-limit">
    <!-- 提示信息 -->
    <view class="tip-info">
      <view class="muted">请注意, 此功能只支持线下店内支付,且不兼容设备开启离线支付。</view>
      <view class="muted">如设置单独消费点限额,优先限制消费点,再限制全局。</view>
    </view>
    <!-- 全局限制 -->
    <view class="setting-section bg-white">
      <view class="section-title">
        <view class="flex row-between col-center">
          <view class="black nr">全局限制</view>
          <u-switch
            :activeColor="variables.colorPrimary"
            v-model="globalLimit.enabled"
            @change="onGlobalLimitChange"
            :size="34"
          ></u-switch>
        </view>
      </view>

      <!-- 每日限制金额 -->
      <view class="setting-item" v-if="globalLimit.enabled">
        <view class="flex row-between col-center">
          <view class="black nr">每日限制金额（元）</view>
          <view class="input-wrapper">
            <custom-stepper
              v-model="globalLimit.dailyAmount"
              :min="1"
              @change="onGlobalAmountChange"
            />
          </view>
        </view>
      </view>
    </view>
    <view class="setting-section bg-white">
      <!-- 单独消费限制设置 -->
      <view class="setting-item">
        <view class="flex row-between col-center">
          <view class="black nr">单独消费限制设置</view>
          <u-switch
            :activeColor="variables.colorPrimary"
            v-model="individualLimit.enabled"
            @change="onIndividualLimitChange"
            :size="34"
          ></u-switch>
        </view>
      </view>
      <!-- 消费点设置列表 -->
      <view class="consumption-points" v-if="individualLimit.enabled">
        <view class="points-list">
          <view class="point-item" v-for="(point, index) in consumptionPoints" :key="index">
            <view class="point-header">
              <view class="flex row-between col-center">
                <view class="black nr">{{ point.name }}</view>
                <u-switch
                  :activeColor="variables.colorPrimary"
                  v-model="point.enabled"
                  @change="onPointLimitChange(index)"
                  :size="34"
                ></u-switch>
              </view>
            </view>

            <!-- 每日限制金额 -->
            <view class="point-amount" v-if="point.enabled">
              <view class="flex row-between col-center">
                <view class="black nr">每日限制金额（元）</view>
                <view class="input-wrapper">
                  <custom-stepper
                    v-model="point.dailyAmount"
                    :min="1"
                    @change="onPointAmountChange(index, $event)"
                  />
                </view>
              </view>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 保存按钮 -->
    <view class="footer bg-white">
      <view class="footer-wrapper flex flex-center">
        <u-button
          text="保存设置"
          shape="circle"
          :color="variables.bgLinearGradient1"
          @click="saveSettings"
          :loading="saving"
        ></u-button>
      </view>
    </view>
  </view>
</template>

<script>
import CustomStepper from '@/components/custom-stepper/custom-stepper.vue'

export default {
  components: {
    CustomStepper
  },
  data() {
    return {
      saving: false,
      // 全局限制设置
      globalLimit: {
        enabled: true,
        dailyAmount: 10
      },
      // 单独消费限制设置
      individualLimit: {
        enabled: true
      },
      // 消费点列表
      consumptionPoints: [
        {
          name: '消费点名称1',
          enabled: true,
          dailyAmount: 200 // 使用数字类型
        },
        {
          name: '消费点名称2',
          enabled: false,
          dailyAmount: 200
        },
        {
          name: '消费点名称3',
          enabled: true,
          dailyAmount: 200
        },
        {
          name: '消费点名称4',
          enabled: true,
          dailyAmount: 200
        },
        {
          name: '消费点名称5',
          enabled: false,
          dailyAmount: 200
        },
        {
          name: '消费点名称6',
          enabled: true,
          dailyAmount: 200
        }
      ]
    }
  },

  methods: {
    // 全局限制开关变化
    onGlobalLimitChange(value) {
      this.globalLimit.enabled = value
    },

    // 单独消费限制开关变化
    onIndividualLimitChange(value) {
      this.individualLimit.enabled = value
    },

    // 消费点限制开关变化
    onPointLimitChange(index) {
      console.log(`消费点${index + 1}限制状态:`, this.consumptionPoints[index].enabled)
    },

    // 全局金额变化
    onGlobalAmountChange(value) {
      console.log('全局金额变化:', value)
    },

    // 消费点金额变化
    onPointAmountChange(index, value) {
      console.log(`消费点${index + 1}金额变化:`, value)
    },

    // 加载当前设置
    async loadSettings() {
      try {
        // 这里添加获取当前设置的API调用
        // const response = await api.getConsumptionLimitSettings()
        // this.globalLimit = response.globalLimit
        // this.individualLimit = response.individualLimit
        // this.consumptionPoints = response.consumptionPoints
      } catch (error) {
        console.error('加载设置失败:', error)
      }
    },

    // 保存设置
    async saveSettings() {
      this.saving = true
      try {
        // 这里添加保存设置的API调用
        const settings = {
          globalLimit: this.globalLimit,
          individualLimit: this.individualLimit,
          consumptionPoints: this.consumptionPoints
        }

        console.log('保存设置:', settings)

        // 模拟API调用
        await new Promise(resolve => setTimeout(resolve, 1000))

        uni.showToast({
          title: '保存成功',
          icon: 'success'
        })

        // 可以选择保存后返回上一页
        // setTimeout(() => {
        //   uni.navigateBack()
        // }, 1500)
      } catch (error) {
        console.error('保存失败:', error)
        uni.showToast({
          title: '保存失败',
          icon: 'error'
        })
      } finally {
        this.saving = false
      }
    }
  },

  onLoad() {
    // 页面加载时可以从API获取当前设置
    this.loadSettings()
  }
}
</script>

<style lang="scss" scoped>
.consumption-limit {
  height: calc(100vh - 180rpx);
  overflow: auto;
  padding: 40rpx;
  background-color: $background-color;

  .tip-info {
    margin-bottom: 20rpx;

    text {
      font-size: 24rpx;
      line-height: 1.5;
    }
  }
  .setting-section {
    margin-bottom: 20rpx;
    border-radius: 20rpx;
    padding: 0 40rpx;

    .section-title {
      padding: 40rpx 0;
      border-bottom: 1rpx solid $border-color-base;
    }
    .setting-item {
      padding: 20rpx 0;
      &:not(:last-child) {
        border-bottom: 1rpx solid $border-color-base;
      }
    }

  }
  .consumption-points {
    border-radius: 20rpx;
    margin-bottom: 20rpx;
    .points-list {
      .point-item {
        &:not(:last-child) {
          border-bottom: 1rpx solid $border-color-base;
        }
        .point-header {
          padding: 40rpx 0;
        }
        .point-amount {
          padding-bottom: 40rpx;
        }
      }
    }
  }

  .footer {
    width: 100%;
    position: fixed;
    bottom: 0;
    left: 0;
    padding: 0 40rpx;
    border-top: 1px solid #eaecee;
    padding-bottom: env(safe-area-inset-bottom);

    .footer-wrapper {
      padding-top: 20rpx;
      padding-bottom: 20rpx;
    }
  }
}
</style>
