<template>
  <view class="custom-stepper">
    <view 
      class="stepper-btn minus" 
      @click="decrease" 
      :class="{ disabled: currentValue <= min }"
    >
      <u-icon name="minus" color="inherit" size="20rpx"></u-icon>
    </view>
    <input 
      :style="{ width: inputWidth + 'rpx' }"
      class="stepper-input" 
      type="number" 
      :value="currentValue" 
      @input="onInput"
      @blur="onBlur"
    />
    <view class="stepper-btn plus" @click="increase">
      <u-icon name="plus" color="inherit" size="20rpx"></u-icon>
    </view>
  </view>
</template>

<script>
export default {
  name: 'CustomStepper',
  props: {
    // 当前值
    value: {
      type: [Number, String],
      default: 1
    },
    // 最小值
    min: {
      type: Number,
      default: 1
    },
    // 最大值
    max: {
      type: Number,
      default: Infinity
    },
    // 步长
    step: {
      type: Number,
      default: 1
    },
    // 输入框宽度
    inputWidth: {
      type: Number,
      default: 120
    }
  },
  
  data() {
    return {
      currentValue: this.value,
      changeTimer: null // 用于防抖的定时器
    }
  },
  
  watch: {
    value: {
      handler(newVal) {
        this.currentValue = newVal
      },
      immediate: true
    }
  },
  
  methods: {
    // 减少
    decrease() {
      if (this.currentValue > this.min) {
        const newValue = Number(this.currentValue) - this.step
        const finalValue = Math.max(newValue, this.min)
        // 立即更新显示值
        this.currentValue = finalValue
        this.$emit('input', finalValue)
        // 防抖处理 change 事件
        this.debouncedChange(finalValue)
      }
    },

    // 增加
    increase() {
      const newValue = Number(this.currentValue) + this.step
      const finalValue = Math.min(newValue, this.max)
      // 立即更新显示值
      this.currentValue = finalValue
      this.$emit('input', finalValue)
      // 防抖处理 change 事件
      this.debouncedChange(finalValue)
    },

    // 防抖处理 change 事件
    debouncedChange(value) {
      // 清除之前的定时器
      if (this.changeTimer) {
        clearTimeout(this.changeTimer)
      }
      // 设置新的定时器，100ms 后触发 change 事件
      this.changeTimer = setTimeout(() => {
        this.$emit('change', value)
      }, 100)
    },

    // 输入处理
    onInput(event) {
      const value = event.target.value
      this.currentValue = value
      // 实时更新，但不验证（允许用户输入过程中的临时状态）
      this.$emit('input', value)
    },

    // 失焦处理
    onBlur(event) {
      const value = event.target.value
      const validatedValue = this.validateValue(value)
      if (validatedValue !== this.currentValue) {
        this.currentValue = validatedValue
        this.$emit('input', validatedValue)
        this.$emit('change', validatedValue)
      }
    },

    // 验证并更新值
    validateValue(value) {
      const numValue = Number(value)

      // 如果是无效值，设为最小值
      if (value === '' || isNaN(numValue) || numValue < this.min) {
        return this.min
      }

      // 如果超过最大值，设为最大值
      if (numValue > this.max) {
        return this.max
      }

      // 确保是整数
      return Math.floor(numValue)
    }
  },

  // 组件销毁时清理定时器
  beforeDestroy() {
    if (this.changeTimer) {
      clearTimeout(this.changeTimer)
    }
  }
}
</script>

<style lang="scss" scoped>
.custom-stepper {
  display: flex;
  align-items: center;
  border: 1px solid #e5e5e5;
  border-radius: 8rpx;
  overflow: hidden;
  background-color: #fff;

  .stepper-btn {
    width: 60rpx;
    height: 60rpx;
    display: flex;
    justify-content: center;
    align-items: center;
    background-color: #f8f9fa;
    border: none;
    cursor: pointer;
    transition: background-color 0.1s ease; // 缩短过渡时间，提升响应速度
    user-select: none; // 防止选中文本
    -webkit-tap-highlight-color: transparent; // 移除移动端点击高亮

    &.minus {
      border-right: 1px solid #e5e5e5;
      color: $color-primary;

      &.disabled {
        color: #ccc;
        cursor: not-allowed;
        opacity: 0.5;
        pointer-events: none; // 禁用点击事件，提升性能
      }

      &:not(.disabled) {
        &:active {
          background-color: #e9ecef;
          transform: scale(0.95); // 添加缩放效果，增强反馈
        }

        // 添加 hover 效果（PC端）
        &:hover {
          background-color: #f1f3f4;
        }
      }
    }

    &.plus {
      border-left: 1px solid #e5e5e5;
      background-color: $color-primary;
      color: #fff;

      &:active {
        background-color: var(--color-primary-dark-1, #409eff);
        transform: scale(0.95); // 添加缩放效果，增强反馈
      }

      // 添加 hover 效果（PC端）
      &:hover {
        background-color: var(--color-primary-light-1, #66b1ff);
      }
    }
  }

  .stepper-input {
    flex: 1;
    height: 60rpx;
    border: none;
    outline: none;
    text-align: center;
    font-size: 28rpx;
    background-color: #fff;
    min-width: 80rpx;

    &::placeholder {
      color: #999;
    }

    // 隐藏数字输入框的上下箭头
    &::-webkit-outer-spin-button,
    &::-webkit-inner-spin-button {
      -webkit-appearance: none;
      margin: 0;
    }

    // Firefox
    &[type="number"] {
      -moz-appearance: textfield;
      appearance: textfield;
    }
  }
}
</style>
