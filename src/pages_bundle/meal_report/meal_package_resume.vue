<template>
  <view :style="theme.style" class="meal-package-resume">
    <view class="card-white">用餐食堂：{{orderInfo.organization_name}}</view>
    <view class="meal-type m-t-30" v-if="allPackRefundList.length">
      <view
        v-for="(item, index) in allMealTypeList"
        :key="index"
        class="meal-type-item"
        :class="[mealType ===item.key ? 'meal-type-item-active' : '']"
        @click="changeMealType(item)"
      >{{item.name}}</view>
    </view>
    <view class="meal-info-wrap m-t-15" v-if="allPackRefundList.length">
      <view class="flex row-between p-t-30">
        <view>可恢复就餐餐段</view>
        <u-checkbox-group v-model="checkedAll" @change="changeAll">
          <u-checkbox :activeColor="variables.colorPrimary" name="all" key="all">全选</u-checkbox>
        </u-checkbox-group>
      </view>
      <view class="meal-list m-t-30">
        <u-checkbox-group v-model="checkedItem" @change="changeItem">
          <view v-for="(item, index) in currentPackRefundList" :key="index" :class="['meal-item', item.disabled ? 'muted' : '']">
            <u-checkbox :activeColor="variables.colorPrimary" :name="item.trade_no" :key="item.trade_no"></u-checkbox>
            <view>{{timeFormat(new Date(item.report_date), 'mm月dd日 星期w')}} {{item.meal_type_alias}}</view>
          </view>
        </u-checkbox-group>
      </view>
    </view>
    <view class="package-list m-t-30" v-else>
      <view class="flex flex-center">
        <u-image width="200rpx" height="200rpx" src="/static/images/mescroll-empty.png"></u-image>
      </view>
      <view class="muted text-center m-t-40">~ 空空如也 ~</view>
    </view>
    <view class="btn-wrap">
      <view class="save-btn" @click="handleMealPackageResume">恢复就餐</view>
    </view>
  </view>
</template>

<script>
import { getApiMealPackOrderRefundList, getApiMealPackOrderResume } from '@/api/report_meal.js'
import { timeFormat } from '@/utils/date.js'
export default {
  data() {
    return {
      paramsData: {},
      orderInfo: {},
      allPackRefundList: [],
      currentPackRefundList: [],
      loading: false,
      mealType: "all",
      allMealTypeList: [{
        key: "all",
        name: "全部"
      }],
      checkedAll: [],
      checkedItem: []
    }
  },
  onShow() {
	},
  onLoad() {
    this.paramsData = this.$store.state.appoint.select
    this.orderInfo = this.$Route.query.data
    let mealTypeList = []
    this.orderInfo.meal_type.map((meal, index) => {
      mealTypeList.push({
        key: meal,
        name: this.orderInfo.meal_type_alias[index]
      })
    })
    this.allMealTypeList = this.allMealTypeList.concat(mealTypeList)
    this.getReportMealPackRefundList()
  },
  methods: {
    timeFormat,
    getReportMealPackRefundList() {
			this.$showLoading({
				mask: true
			})
			getApiMealPackOrderRefundList({
				report_meal_pack_settings_id: this.orderInfo.id,
        org_id: this.orderInfo.organization
      })
				.then(res => {
					uni.hideLoading()
					if (res.code == 0) {
						this.allPackRefundList = res.data.results.map(item => {
              item.checked = false
              return item
            })
            this.currentPackRefundList = this.allPackRefundList
					} else {
						uni.$u.toast(res.msg)
					}
				})
				.catch(error => {
					uni.$u.toast(error.message)
				})
		},
    changeMealType(item) {
      this.mealType = item.key
      if (item.key === 'all') {
        this.currentPackRefundList = this.allPackRefundList
      } else {
        this.currentPackRefundList = this.allPackRefundList.filter(order => order.meal_type_alias === item.name)
      }
      this.checkedItem = []
      this.checkedAll = []
    },
    changeAll(e) {
      this.checkedAll = e
      if (this.checkedAll.length) {
        this.checkedItem = this.currentPackRefundList.map(item => item.trade_no)
      } else {
        this.checkedItem = []
      }
    },
    changeItem(e) {
      this.checkedItem = e
      if (this.checkedItem.length === this.currentPackRefundList.length) {
        this.checkedAll = ['all']
      } else {
        this.checkedAll = []
      }
    },
    handleMealPackageResume() {
      this.$showLoading({
				mask: true
			})
			getApiMealPackOrderResume({
        trade_nos: this.checkedItem,
        report_meal_pack_settings_id: this.orderInfo.id,
				person_no: this.paramsData.person.person_no,
        org_id: this.orderInfo.organization
      })
				.then(res => {
					uni.hideLoading()
					if (res.code == 0) {
						this.getReportMealPackRefundList()
					} else {
						uni.$u.toast(res.msg)
					}
				})
				.catch(error => {
					uni.$u.toast(error.message)
				})
    }
  }
}
</script>

<style lang="scss">
.meal-package-resume {
  padding: 30rpx;
  .card-white{
    background-color: #fff;
    border-radius: 20rpx;
    padding: 30rpx;
  }
  .meal-type{
    display: flex;
    flex-wrap: wrap;
    .meal-type-item{
      background-color: #fff;
      padding: 10rpx 30rpx;
      margin-right: 20rpx;
      border-radius: 12rpx;
      margin-bottom: 15rpx;
    }
    .meal-type-item-active{
      background-color: $color-primary;
      color: #fff;
    }
  }
  .meal-info-wrap {
		flex: 1;
    border-radius: 20rpx;
    background-color: #fff;
    position: relative;
    padding: 0 30rpx;
    margin-bottom: 30rpx;

    &::before,
    &::after {
      content: '';
      display: block;
      height: 30rpx;
      width: 30rpx;
      background-color: $background-color;
      position: absolute;
      border-radius: 50%;
      top: 90rpx;
      transform: translateY(-50%);
    }

    &::before {
      left: -15rpx;
    }

    &::after {
      right: -15rpx;
    }
	}
  .meal-list{
    padding: 20px 0;
    border-top: #e0e0e0 dashed 1px;
    margin-bottom: 150rpx;
    .u-checkbox-group{
      display: block;
    }
  }
  .meal-item{
    display: flex;
    line-height: 54rpx;
  }
  .btn-wrap{
    position: absolute;
    bottom: 10rpx;
    left: 0;
    right: 0;
  }
  .save-btn {
    width: 90%;
    color: #ffffff;
    background-color: $color-primary;
    text-align: center;
    font-size: 30rpx;
    padding: 20rpx 0;
    border-radius: 100rpx;
    margin: 0 auto;
    border: 1px solid $color-primary;
  }
  
}
</style>
