<!-- 个人中心 -->
<template>
  <view :style="theme.style" :class="['user', platform === 'psbc'?'p-t-20':'']" @touchmove="popupShow = true" @touchend="popupShow = false">
    <view v-if="platform === 'psbc'" class="navbar">
      <u-navbar title="个人中心" :fixed="true" @leftClick="gotoIndexPage">
      </u-navbar>
    </view>
    <!-- 个人信息 -->
    <view class="user-info flex row-between col-center">
      <view class="flex col-center">
        <view class="user-avatar">
          <u-avatar :src="userInfo.headimgurl ? userInfo.headimgurl : themeImgPath.img_avatar"></u-avatar>
        </view>
        <view class="flex-col m-l-20">
          <view class="flex col-center" v-longpress="showVconsoleHandle">
            <view v-if="!isAddressVisitor" class="xxl line-1 bold no-select">{{ userInfo.nickname ? userInfo.nickname : '' }}</view>
            <view v-else class="xxl line-1 bold no-select">游客</view>
            <!--<view class="level mini white m-l-10" v-if="userInfo.nickname || userInfo.phone">普通会员</view>-->
          </view>
					<view class="flex">
						<view v-if="userInfo.phone" class="nr muted p-r-20">{{userInfo.phone.toString().slice(0,3)+'****'+userInfo.phone.toString().slice(-4)}}</view>
						<u-icon v-if="false" color="inherit" :name="personalInfo.showUserPhone?'eye-fill':'eye-off'" size="30"  @click="clickShowInformation(!personalInfo.showUserPhone)"></u-icon>
					</view>
        </view>
      </view>
      <view class=""  @click="$miRouter.push({ path: '/pages_info/account_info/detail', query: { type: 'user' } })">
        <u-icon color="#00c8c8" name="arrow-right" size="30rpx"></u-icon>
      </view>
    </view>
    <view v-if="!isAddressVisitor&&showVIPContent" :class="['member-content', 'flex', isVIPMember ? 'row-left' : 'row-right', 'p-r-20', 'm-t-20', isVIPMember ? 'is-member' : 'no-member']"
    	@click="gotoMemberCenter">
      <!--
    	<view class="flex flex-center">
    		<image class="advise-img m-r-12" :src="themeImgPath.img_member"></image>
    		<view class="lg m-t-8 f-w-500">
    			会员中心
    		</view>
    	</view>
    	<view class="flex flex-center">
    		<view class="nr muted ">
    			解锁更多会员权益
    		</view>
    		<view class="m-l-16">
    			<u-icon color="#acafb1" name="arrow-right" size="30rpx"></u-icon>
    		</view>
    	</view>-->
      <view class="member-content-inside flex flex-center" :style="isVIPMember ? {height: '178rpx'} : {height: '136rpx'}">
        <view class="member-content-inside-button" v-if="!isVIPMember">
          <u--image :src="themeImgPath.goToVIPPage_btn_1" width="144rpx" height="54rpx"></u--image>
        </view>
        <view class="member-content-inside-text" v-if="isVIPMember">
          <span v-if="!$store.state.app.membershipRights.permanent_status">会员有效期至{{ endTime }}</span>
          <span v-if="$store.state.app.membershipRights.permanent_status">永久会员</span>
        </view>
      </view>
    </view>
    <!--  -->
    <view class="flex flex-between flex-1 bg-white user-assets" v-if="false">
      <view class="flex-col row-center text-center flex-1" @click="$toast({ title: '快马加鞭制作中！' })">
        <view class="font-size-42 m-b-14">0</view>
        <view class="mini f-w-500">成长值</view>
      </view>
      <view class="flex-col row-center text-center flex-1" @click="$toast({ title: '快马加鞭制作中！' })">
        <view class="font-size-42 m-b-14">0</view>
        <view class="mini f-w-500">积分</view>
      </view>
      <!-- 我的项目 -->
      <!-- @click="$miRouter.push('/page_info/account_info/user_items')" -->
      <view class="flex-col row-center text-center flex-1" @click="$toast({ title: '快马加鞭制作中！' })">
        <view class="font-size-42 m-b-14">
          {{ userInfo.company ? userInfo.company.length : 0 }}
        </view>
        <view class="mini f-w-500">我的项目</view>
      </view>
    </view>
    <!-- 我的服务 -->
    <view class="bg-white m-t-20 user-menu" v-if="menuList[0].length">
      <view class="font-size-30 f-w-600 user-menu-title">我的服务</view>
			<view class="menu-lists">
				<view class="swiper-card">
					<swiper :style="{height: menuHeight+'px'}" :indicator-dots="false" :autoplay="false" @change="changeSwiper">
						<swiper-item v-for="(row, i) in menuList" :key="i">
							<view class="flex flex-wrap">
								<view
									v-for="(item, index) in row"
									:class="['menu-item flex flex-col col-center', index<5?'m-b-30':'']"
									:key="index"
									@click="handleClickMenu(item)"
								>
									<u-image width="60rpx" height="60rpx" :src="item.icon"></u-image>
									<view class="m-t-16 xs">{{ item.name }}</view>
								</view>
							</view>
						</swiper-item>
					</swiper>
					<!-- 指示器 -->
					<view class="swiper-dot-wrapper flex flex-center"  v-if="menuList.length>1">
						<view v-for="(j, k) in menuList" :key="k" :class="['swiper-dot', currentSwiper===k?'active':'']"></view>
					</view>
				</view>
			</view>
    </view>

    <!-- 地址管理 -->
    <!-- <view class="flex row-between flex-center flex-1 address bg-white m-t-20"
			@click="goToPager('/pages_bundle/address/address_manage')">
			<view class="flex flex-center">
				<image class="address-img m-r-12" :src="themeImgPath.img_my_address"></image>
				<view class="nr f-w-500">
					地址管理
				</view>
			</view>
			<view class="m-l-16">
				<u-icon color="#acafb1" name="arrow-right" size="30rpx"></u-icon>
			</view>
		</view> -->
    <!-- 体检报告 -->
		<view v-if="userInfo && userInfo.phone && !isAddressVisitor" class="list-setting-box m-t-30 bg-white">
      <view class="p-l-30 p-t-30 f-w-600">更多服务</view>
      <template v-for="item in funEntry">
        <view
          v-if="!item.hidden"
          class="list-setting-item flex row-between flex-center flex-1 address bg-white"
          :key="item.type"
          @click="goToPager(item.path, item.type)"
        >
          <view class="flex flex-center">
            <image class="address-img m-r-12 img-filter" :src="item.imgIcon"></image>
            <view class="nr f-w-500">
              {{ item.name ? item.name : '' }}
            </view>
            <view v-if="item.type==='notice' && messageShow" class="notice-num m-l-10"></view>
            <view v-if="item.type==='proposal' && (noticeBadge.system_feedback || noticeBadge.has_satisfaction_reply)" class="notice-num m-l-10"></view>
          </view>
          <view class="flex flex-center">
            <view class="muted m-r-16">{{ item.tips ? item.tips : '' }}</view>
            <u-icon color="#acafb1" name="arrow-right" size="30rpx"></u-icon>
          </view>
        </view>
      </template>
		</view>
    <!-- 建议反馈 -->
    <!-- <view class="flex row-between flex-center flex-1 advise bg-white m-t-20"
			@click="goToPager('/pages_info/feedback/proposal_complaint')">
			<view class="flex flex-center">
				<image class="advise-img m-r-12" :src="themeImgPath.img_my_advise"></image>
				<view class="nr f-w-500">
					建议反馈
				</view>
			</view>
			<view class="flex flex-center">
				<view class="nr muted ">
					期待您的反馈
				</view>
				<view class="m-l-16">
					<u-icon color="#acafb1" name="arrow-right" size="30rpx"></u-icon>
				</view>
			</view>
		</view> -->
    <view class="flex row-between flex-center flex-1 advise bg-white m-t-20"
			@click="goToPager('/pages_info/user_config/index')">
			<view class="flex flex-center">
        <image class="icon-img m-r-12 img-filter" :src="themeImgPath.img_icon_set"></image>
				<view class="nr f-w-500">
					设置
				</view>
			</view>
			<view class="flex flex-center">
				<!-- <view class="nr muted ">
					协议
				</view> -->
				<view class="m-l-16">
					<u-icon color="#acafb1" name="arrow-right" size="30rpx"></u-icon>
				</view>
			</view>
		</view>
    <!-- 只有农行abc_frontend 分流页 才有这个按钮 -->
    <view class="flex row-between flex-center flex-1 advise bg-white m-t-20"
			@click="goToPager('abc','v3')" v-if="index_query.shunt_type==='v3'">
			<view class="flex flex-center">
        <!-- <image class="icon-img m-r-12 img-filter" :src="themeImgPath.img_icon_set"></image> -->
				<view class="nr f-w-500">
					返回食堂列表
				</view>
			</view>
			<view class="flex flex-center">
				<!-- <view class="nr muted ">
					协议
				</view> -->
				<view class="m-l-16">
					<u-icon color="#acafb1" name="arrow-right" size="30rpx"></u-icon>
				</view>
			</view>
		</view>
    <view v-if="backUrl" class="flex row-between flex-center flex-1 advise bg-white m-t-20"
			@click="backToServerPage">
			<view class="flex flex-center">
				<!-- <image class="advise-img m-r-12" :src="themeImgPath.img_my_advise"></image> -->
				<view class="nr f-w-500">
					切换食堂
				</view>
			</view>
			<view class="flex flex-center">
				<view class="nr muted ">
				</view>
				<view class="m-l-16">
					<u-icon color="#acafb1" name="arrow-right" size="30rpx"></u-icon>
				</view>
			</view>
		</view>
    <!--弹窗-->
    <non-member :showMember="showMember" :content="modalContent" :show-confirm-button="showConfirmbtn" @setShowMember="setShowMember"></non-member>
    <!-- 退出登陆 -->
		<custom-tabbar type="user"></custom-tabbar>
    <!-- 客服弹窗 -->
		<CustomerServicePopup :popupShow="popupShow" />
  </view>
</template>

<script>
import { setUserLogout, getUserMessageCount } from '@/api/app'
import { apiPsbcFkToken } from '@/api/sign.js'
import { mapActions, mapGetters, mapMutations } from 'vuex'
import checkMember from '@/mixins/checkMember.js'
import Cache from '@/utils/cache'
import CustomerServicePopup from '@/components/customer-service-popup/customer-service-popup.vue'

// #ifdef H5
import VConsole from 'vconsole'
// #endif
import { getFeedbackReadState } from '@/utils/getNotice'

import dayjs from '../../uni_modules/uview-ui/libs/util/dayjs'


export default {
  components:{
    CustomerServicePopup
  },
  mixins: [checkMember],
  data() {
    return {
      popupShow: false,
      showVIPContent: false,
      isVIPMember: false,
      // imgPath: this.$imgPath,
      userInfo: {},
			currentSwiper: 0,
			// 一个数组只能有10个数据，添加新的数组
			menuList:[
				[
					// {
					// 	icon: this.$imgPath.img_icon_my_fuwu_notice,
					// 	name: '消息通知',
					// 	path: '/pages_info/news/project_news',
					// 	type: 'projectNews',
					// },
					// {
					// 	icon: require('@/static/icons/icon_my_fuwu_order.png'),
					// 	name: '我的订单',
					// 	path: '/pages_order/order/order_lists',
					// 	type: 'order',
					// },
					// {
					// 	icon:this.$imgPath.img_icon_my_fuwu_pingjia,
					// 	name: '我的评价',
					// 	path: '/pages_info/evaluate/user_evaluate',
					// 	type: 'evaluate',
					// },
					// {
					// 	icon: this.$imgPath.img_icon_my_fuwu_money,
					// 	name: '钱包中心',
					// 	path: '/pages_info/wallet/wallet_center',
					// 	type: 'evaluate',
					// },
     //      {
					//  icon: this.$imgPath.img_invoice_icon,
					// 	name: '开票记录',
					// 	path: '/pages_order/invoice/list',
					// 	type: 'invoice',
					// },
					// {
					// 	icon: this.$imgPath.img_icon_my_fuwu_renyuan,
					// 	name: '关联人员',
					// 	path: '/pages_bundle/related_personnel/related_personnel',
					// 	type: 'relatedPersonnel',
					// },
				],
			],
      // funEntry: [
      // {
      //     name: '体检中心',
      //     type: 'check_center',
      //     path: '/pages_health_pomr/examination_report/index',
      //     tips: '查看报告',
      //     imgIcon: this.$imgPath.img_ic_check_center
      //   },
      //   {
      //     name: '自定义食物',
      //     type: 'food_custom',
      //     path: '/pages_health/record_food/food_custom_list',
      //     tips: '添加自己的食物进行记录',
      //     imgIcon: this.$imgPath.img_canteen_caipu
      //   },
      //   {
      //     name: '消息通知',
      //     type: 'notice',
      //     path: '/pages_info/news/project_news',
      //     tips: '系统通知',
      //     imgIcon: this.$imgPath.img_icon_new
      //   },
      //   // {
      //   //   name: '地址管理',
      //   //   type: 'address',
      //   //   path: '/pages_bundle/address/address_manage',
      //   //   tips: '',
      //   //   imgIcon: require('@/static/icons/my_address.png')
      //   // },
      //   // {
      //   //   name: '体检报告',
      //   //   type: 'test',
      //   //   path: '/pages_health/healthy/body_management/examination_report',
      //   //   tips: '',
      //   //   imgIcon: require('@/static/icons/report.png')
      //   // },
      //   {
      //     name: '建议反馈',
      //     type: 'proposal',
      //     path: '/pages_info/feedback/system_advise',
      //     tips: '反馈系统使用问题',
      //     imgIcon: this.$imgPath.img_icon_advise
      //   },
      //   // {
      //   //   name: '健康目标',
      //   //   type: 'health_target',
      //   //   path: '',
      //   //   tips: '暂未上线',
      //   //   imgIcon: require('@/static/icons/health_target.png')
      //   // },
      //   // {
      //   //   name: '设置',
      //   //   type: 'setUp',
      //   //   path: '/pages_info/user_config/index',
      //   //   tips: '',
      //   //   imgIcon: this.$imgPath.img_icon_set
      //   //   // imgIcon: require('@/static/icons/setUp.png')
      //   // },
      //   // {
      //   //   name: '审核查询',
      //   //   type: 'review',
      //   //   path: '/pages_order/review/index',
      //   //   tips: '',
      //   //   imgIcon: require('@/static/icons/review.png')
      //   // },
      //   // #ifdef MP-WEIXIN || MP-ALIPAY
      //   // {
      //   //   name: '免密设置',
      //   //   type: 'free_payment_setting',
      //   //   path: '/pages_info/user_config/free_payment_setting',
      //   //   tips: '',
      //   //   imgIcon: require('@/static/icons/pwd_setting.png'),
      //   //   hidden: true
      //   // }
      //   // #endif
      // ],
      navBg: 0,
      backUrl: '',
      visitorMenuList: [
				[
					{
						icon: this.$imgPath.img_icon_my_fuwu_order,
						name: '我的订单',
						path: '/pages_order/order/order_lists',
						type: 'order',
					},
					{
						icon: this.$imgPath.img_icon_my_fuwu_pingjia,
						name: '我的评价',
						path: '/pages_info/evaluate/user_evaluate',
						type: 'evaluate',
					},
          {
						icon: this.$imgPath.img_invoice_icon,
						name: '开票记录',
						path: '/pages_order/invoice/list',
						type: 'invoice',
					},
					{
						icon: this.$imgPath.img_icon_youke_reservation,
						name: '我的预约',
						path: '/pages_bundle/appoint/user_appoint',
						type: 'myReservation',
					},
          {
						icon: this.$imgPath.img_icon_youke_order_review,
						name: '申诉查询',
						path: '/pages_order/review/index',
						type: 'order_review',
					}
				],
			],
      messageShow: false,
      index_query: Cache.get('INDEX_QUERY') || {}
    }
  },
  computed: {
    ...mapGetters(['showVconsole','personalInfo', 'platform', 'isAddressVisitor', 'noPermissions', 'noticeBadge']),
    // 菜单栏高度
    menuHeight() {
      let height = uni.upx2px(160)
      if ((this.menuList[0] && this.menuList[0].length > 5) || this.menuList.length > 1) {
        height = this.language === 'en' ? uni.upx2px(320) : uni.upx2px(268)
      } else {
        height = this.language === 'en' ? uni.upx2px(160) : uni.upx2px(140)
      }
      return height
    },
    endTime() {
      let endTime = this.$store.state.app.membershipRights.end_time
      return dayjs(endTime).format('YYYY-MM-DD')
    },
    funEntry() {
      return [
        {
          name: '体检中心',
          type: 'check_center',
          path: '/pages_health_pomr/examination_report/index',
          tips: '查看报告',
          imgIcon: this.themeImgPath.img_ic_check_center
        },
        {
          name: '消费限额',
          type: 'consumption_limit',
          path: '/pages_info/consumption_limit/consumption_limit',
          tips: '去设置',
          imgIcon: this.themeImgPath.img_canteen_caipu
        },
        {
          name: '自定义食物',
          type: 'food_custom',
          path: '/pages_health/record_food/food_custom_list',
          tips: '添加自己的食物进行记录',
          imgIcon: this.themeImgPath.img_canteen_caipu
        },
        {
          name: '消息通知',
          type: 'notice',
          path: '/pages_info/news/project_news',
          tips: '系统通知',
          imgIcon: this.themeImgPath.img_icon_new
        },
        // {
        //   name: '地址管理',
        //   type: 'address',
        //   path: '/pages_bundle/address/address_manage',
        //   tips: '',
        //   imgIcon: require('@/static/icons/my_address.png')
        // },
        // {
        //   name: '体检报告',
        //   type: 'test',
        //   path: '/pages_health/healthy/body_management/examination_report',
        //   tips: '',
        //   imgIcon: require('@/static/icons/report.png')
        // },
        {
          name: '建议反馈',
          type: 'proposal',
          path: '/pages_info/feedback/system_advise',
          tips: '反馈系统使用问题',
          imgIcon: this.themeImgPath.img_icon_advise
        },
        // {
        //   name: '健康目标',
        //   type: 'health_target',
        //   path: '',
        //   tips: '暂未上线',
        //   imgIcon: require('@/static/icons/health_target.png')
        // },
        // {
        //   name: '设置',
        //   type: 'setUp',
        //   path: '/pages_info/user_config/index',
        //   tips: '',
        //   imgIcon: this.$imgPath.img_icon_set
        //   // imgIcon: require('@/static/icons/setUp.png')
        // },
        // {
        //   name: '审核查询',
        //   type: 'review',
        //   path: '/pages_order/review/index',
        //   tips: '',
        //   imgIcon: require('@/static/icons/review.png')
        // },
        // #ifdef MP-WEIXIN || MP-ALIPAY
        // {
        //   name: '免密设置',
        //   type: 'free_payment_setting',
        //   path: '/pages_info/user_config/free_payment_setting',
        //   tips: '',
        //   imgIcon: require('@/static/icons/pwd_setting.png'),
        //   hidden: true
        // }
        // #endif
      ]
    }
  },
  watch: {
		noPermissions(newVal) {
      if (!this.noPermissions && this.$Route.path === '/pages/user/user' ) {
        this.isPermissions()
      }
    },
    '$store.state.app.messageListData': {
      handler(newVal) {
        console.log('$store.state.app.messageListData', newVal)
        if (newVal && !newVal.service_notice_count && !newVal.system_notice_count && !newVal.weekly_report_count) {
          this.messageShow = false
        } else {
          this.messageShow = true
        }
      },
      immediate: true
    }
	},
  onReady() {
    uni.hideTabBar();
  },
  onShow() {
    this.userInfo = Cache.get('userInfo')
    console.log(this.userInfo,98765);
    console.log(this.isAddressVisitor,98767);
    // if (this.userInfo.phone && this.userInfo.company_id) {
    // }
    this.backUrl = Cache.get('backUrl')
    if (!this.isAddressVisitor) {
      this.showVIPContent = Cache.get('isVIP')
      this.isVIPMember = Cache.get('membershipRights').is_member_status
    }
    this.setMembershipRights(Cache.get('userInfo').user_id)
    this.getUserMessageCount()
    console.log(this.showVIPContent, this.platform, this.isVIPMember)
    getFeedbackReadState()
  },
  onLoad(option) {
		uni.hideTabBar()
    this.backUrl = Cache.get('backUrl')
		// #ifdef H5
    this.$nextTick(_ => {
      document.getElementsByTagName('uni-page-head')[0].style.display = 'none'
    })
		// #endif
    if (this.isAddressVisitor) { // 如果是游客，存下信息
      this.menuList = this.visitorMenuList
    }
  },
  methods: {
    ...mapActions({
      setLogOut: 'setLogOut',
      setVconsoleOpts: 'setVconsoleOpts',
      setPersonalInfo: 'setPersonalInfo',
      setMessageListData: 'setMessageListData',
      setMembershipRights: 'setMembershipRights'
    }),
    ...mapMutations(['SET_SELECT','CLEAR_SELECT']),
		changeSwiper(e) {
		  this.currentSwiper = e.detail.current
		},
		handleClickMenu(item) {
      switch (item.type) {
        case 'order':
          this.$miRouter.push({
            path: item.path,
            query: {
              project_point: 1 // 0,1 0代表当前项目点，1代表所有
            }
          })
          break
        default:
						this.$miRouter.push(item.path)
          break
      }
		},
    // 页面跳转
    goToPager(url, type) {
      switch (type) {
        case 'notice':
          this.$miRouter.push({
            path: url,
            query: {
              type: type
            }
          })
          break
        case 'order':
          this.$miRouter.push({
            path: url,
            query: {
              project_point: 1 // 0,1 0代表当前项目点，1代表所有
            }
          })
          break
        case 'free_payment_setting':
          this.$miRouter.push({
            path: url
          })
          break
        case 'v3':
            // 回跳3.0
            location.href = 'https://abc.packertec.com/#/login?shunt_type=v4'
          break
        default:
          if (url) {
            this.$miRouter.push(url)
          }
          break
      }
    },
    async getUserMessageCount() {
      this.$showLoading({
        title: '获取通知中...',
        mask: true
      })
      const [err, res] = await this.$to(getUserMessageCount())
      uni.hideLoading()
      if (err) {
        console.log('获取失败', err)
        return
      }
      if (res.code === 0) {
        this.setMessageListData(res.data)
        let countList = []
        for (let key in res.data) {
          if (key.includes('_count')) {
            countList.push(res.data[key])
          }
        }
        countList.forEach(item => {
          if (item !== 0) {
            this.messageShow = true
          }
        })
      }
    },
    gotoMemberCenter() {
      // this.$miRouter.push('/pages_member/member_center/member_center')
      this.$miRouter.push('/pages_member/member_center/VIP_page')
    },
		clickShowInformation(data){
			this.setPersonalInfo({key:'showUserPhone',value:data})
		},
    gotoLoginHandle() {
      // #ifdef MP-ALIPAY
      this.$miRouter.push({
        path: '/pages/login/mp_login'
      })
      // #endif
      // #ifdef MP-WEIXIN
      this.$miRouter.push({
        path: '/pages/login/wx_login'
      })
      // #endif
      // #ifdef H5
      this.$miRouter.push({
        path: '/pages/login/login'
      })
      // #endif
    },
    showVconsoleHandle() {
      // #ifdef H5
      let showVconsole = sessionStorage.getItem('showVconsole')
      if(showVconsole==1) return
      uni.showModal({
				title: '提示',
				content: `确定打开VConsole吗？`,
				// cancelColor: this.$vars.color_primary,
				// confirmColor: this.$vars.color_red,
				confirmText: '确定',
				cancelText: '取消',
				success: async (res) => {
					if (res.confirm) {
            await this.setVconsoleOpts(true)
            new VConsole()
					} else if (res.cancel) {
						// console.log('用户点击取消')
					}
				}
			})
      // #endif
    },
    // 返回食堂
    backToServerPage() {
      // #ifdef H5
      this.getPsbcFkToken()
      // location.href = this.backUrl
      // #endif
    },
    getPsbcFkToken() {
      apiPsbcFkToken()
        .then(res => {
          if (res.code == 0) {
          } else if (res.code == 302) {
					// #ifdef H5
					window.location.href = res.msg
					// #endif
				} else {
            uni.$u.toast(res.msg)
          }
        })
        .catch(err => {
          uni.$u.toast(err.message)
        })
    },
    gotoIndexPage() {
			this.$miRouter.pushTab({
				path: '/pages/index/index'
			});
		}
  },

  onPageScroll(e) {
    const top = uni.upx2px(100)
    const { scrollTop } = e
    let percent = scrollTop / top > 1 ? 1 : scrollTop / top
    this.navBg = percent
  }
}
</script>

<style lang="scss">
.user {
  .icon-img {
    width: 34rpx;
    height: 34rpx;
  }
  .no-select{
    user-select: none;
  }
  display: flex;
  flex-direction: column;
  background-image: $bg-linear-gradient-11;
  background-size: 750rpx 560rpx;
  background-repeat: no-repeat;
  padding: 0 40rpx;
  padding-top: 50rpx;

  .user-info {
    padding: 36rpx 0 33rpx 0;
    .user-avatar {
      border-radius: 50%;
      border: 4rpx solid #ffffff;
      box-sizing: border-box;
    }

    .level {
      // width: 104rpx;
      // height: 32rpx;
      background: $color-primary;
      border-radius: 16rpx;
      padding: 6rpx 13rpx;
    }

    .icon-go-green {
      height: 30rpx;
      width: 30rpx;
    }
  }

  .is-member {
    margin-top: -40rpx;
    background-image: url($imgBasePath + '/member/newVIP/userPage_isVIP.png');
  }

  .no-member {
    background-image: url($imgBasePath + '/member/newVIP/userPage_noVIP.png');
  }

  .member-content {
    background-size: 100% 100%;
		background-repeat: no-repeat;
    &-inside {
      position: relative;
      height: 136rpx;
      &-text {
        width: 420rpx;
        position: absolute;
        font-size: 22rpx;
        color: #753822;
        bottom: 34rpx;
        left: 30rpx;
      }
    }
  }

  .user-assets {
    padding: 40rpx 0;
    border-radius: 20rpx;
  }

  .user-menu {
    border-radius: 20rpx;

    .user-menu-title {
      padding: 28rpx 0 10rpx 28rpx;
    }
		.menu-lists {
			padding: 0rpx 10rpx 0;
			.swiper-card{
			  background: #ffffff;
			  border-radius: 20rpx;
			  padding:10rpx 0 30rpx 0;
			}
			.menu-item {
				width: 20%;
			}
			.swiper-dot-wrapper{
				border-radius: 200rpx;
				.swiper-dot{
					width: 15rpx;
					height: 4rpx;
					border-radius: 200rpx;
					margin: 0 4rpx;
					transition: transform 0.3s;
					background-color: #e3e3e3;
					&.active{
						width: 20rpx;
						background-color: $color-primary;
					}
				}
			}
		}
  }
  .member-wrap{
    background-color: #fff;
    padding: 30rpx 0;
    border-radius: 20rpx;
    display: flex;
    justify-content: space-around;
    .member-item{
      width: 50%;
      padding-left: 60rpx;
      .member-item-title{
        font-size: 32rpx;
        margin-bottom: 20rpx;
        .member-img{
          margin-right: 10rpx;
        }
      }
      .member-item-text{
        font-size: 26rpx;
        color: #a1a1a1;
      }
    }
    .border-right{
      border-right: 1rpx solid #d3d3d3;
    }
  }

  .address {
    padding: 38rpx 20rpx 38rpx 28rpx;
    border-radius: 20rpx;
    .address-img {
      height: 34rpx;
      width: 34rpx;
    }
    .icon-go-grey {
      height: 30rpx;
      width: 30rpx;
    }
  }

  .notice-num{
    width: 18rpx;
    height: 18rpx;
    border: 1rpx solid #FF5353;
    background-color: #FF5353;
    border-radius: 50%;
  }

  .advise {
    padding: 38rpx 20rpx 38rpx 28rpx;
    border-radius: 20rpx;

    .advise-img {
      height: 44rpx;
      width: 44rpx;
    }

    .icon-go-grey {
      height: 30rpx;
      width: 30rpx;
    }
  }

  .logout {
    margin: 30rpx 0 80rpx;
    border-radius: 20rpx;
    padding: 32rpx 0;
    text-align: center;
    font-size: 28rpx;
    font-weight: 500;
    color: #ff6c6c;
  }
  .list-setting-box{
    border-radius: 20rpx;
    overflow: hidden;
    .list-setting-item{
      position: relative;
      border-radius: 0;
      &:not(:last-child) {
        &:after{
          position: absolute;
          content: "";
          left: 30rpx;
          right: 30rpx;
          bottom: 0;
          height: 1rpx;
          background-color: #f1f1f1;
        }
      }
    }
  }
}
</style>
