<template>
  <view :style="theme.style" class="meal-package-confirm">
    <view class="card-white">用餐食堂：{{orderInfo.organization_name}}</view>
    <view class="meal-type m-t-30">
      <view
        v-for="(item, index) in allMealTypeList"
        :key="index"
        class="meal-type-item"
        :class="[mealType ===item.key ? 'meal-type-item-active' : '']"
        @click="changeMealType(item)"
      >{{item.name}}</view>
    </view>
    <view class="meal-list card-white m-t-30">
      <view v-for="(item, index) in currentOrderList" :key="index" :class="['meal-item', item.disabled ? 'muted' : '']">
        <view>{{item.monthDate}} {{item.meal_type_name}}</view>
        <view>￥{{divide(item.report_fee)}}</view>
      </view>
    </view>
    <!-- Footer 底部 Start -->
    <view class="footer">
      <view v-if="false && Number(agreementList.length)" class="m-b-20 m-l-40">
        <u-checkbox-group v-model="readAgreement" @change="changeAgreement">
          <u-checkbox name="package" :activeColor="variables.colorPrimary" size="28rpx">
            <view class="agreement-label">
              <text>我已阅读并同意</text>
              <text
                v-for="(agreement, index) in agreementList"
                :key="index"
                style="color: #6ae3a4"
                @click="showAgreement = true"
              >《{{ agreement.agreement_type_alias }}》</text>
            </view>
          </u-checkbox>
        </u-checkbox-group>
      </view>
      <view class="bg-white footer--warpper flex row-between">
        <view class="muted flex col-center">
          <text class="m-r-20 md">合计</text>
          <price-format :price="totalFee" :size="40" color="#000000"></price-format>
        </view>
        <view class="submit-btn white flex flex-center" :class="!canBuyList.length ? 'un-submit-btn' : ''" @click="onSubmitOrder">提交订单</view>
      </view>
    </view>
    <!-- Footer End -->
    <u-popup :show="showAgreement" :round="30" @close="showAgreement = false">
      <view class="agreement-popup">
        <view class="popup-title">订餐须知</view>
        <scroll-view style="max-height: 480rpx" :scroll-y="true" @scrolltolower="scrollBottom">
          <view class="info-title">一、餐包预订</view>
          <view class="m-b-15">预订方式：学生需通过学校指定的方式（如在线系统、纸质表格等）完成餐包预订。预订截止时间为每周五下午5点，以便食堂提前准备。</view>
          <view class="m-b-15">变更与取消：一旦完成预订，若因特殊原因需要更改或取消，请至少提前一天通知学校食堂，以便做出相应调整。未能及时通知将可能无法退款或调整.</view>
          <view class="m-b-25">支付方式：餐费需按照学校规定的时间内完成支付，逾期未支付者视为自动放弃本次预订。</view>
          <view class="info-title">二、食物过敏信息告知</view>
          <view class="m-b-15">重要性：为避免食物引起的不良反应，特别是过敏情况的发生，请每位学生或其监护人在首次预订餐包时提供详细的食物过敏信息。</view>
          <view class="m-b-15">信息更新：如果学生的食物过敏状况发生变化，请及时更新相关信息给学校食堂，以确保能够为学生提供安全的饮食。</view>
          <view class="m-b-25">责任声明：学校将尽最大努力确保提供的食物符合学生的饮食需求，但对于因学生未及时提供准确的食物过敏信息而导致的问题，学校不承担相关责任。</view>
          <view class="info-title">三、其他注意事项</view>
          <view class="m-b-15">餐食领取：请按照指定时间和地点领取餐包，过时不候。</view>
          <view class="m-b-15">反馈机制：鼓励学生及其家长对餐食质量、服务态度等方面提出宝贵意见，共同促进服务质量的提升。</view>
          <view class="m-b-25">环保倡议：提倡使用可重复使用的餐具，减少一次性餐具的使用，共建绿色校园。</view>
        </scroll-view>
        <view class="m-t-25">
          <u-button :disabled="!isReadAgreement" text="我已经阅读" @click="confirmPopup" shape="circle" :color="variables.colorPrimary"></u-button>
        </view>
      </view>
		</u-popup>
  </view>
</template>

<script>
import { getApiReportMealPackOrderCreate } from '@/api/report_meal.js'
import { PACKAGEAGREEMENT } from '@/constants/agreement'
import { divide } from '@/utils/util.js'
import { timeFormat } from '@/utils/date.js'
import Cache from '@/utils/cache'
export default {
  data() {
    return {
      loading: false,
      mealType: "all",
      allMealTypeList: [{
        key: "all",
        name: "全部"
      }],
      allOrderList: [],
      currentOrderList: [],
      totalFee: 0,
      mealKey: { // 后台报餐预约餐段key遗留问题
        breakfast: 'breakfast',
        lunch: 'lunch',
        afternoon: 'hit_tea',
        dinner: 'dinner',
        supper: 'midnight',
        morning: 'early',
      },
      paramsData: {},
      readAgreement: [], // 已读协议
      agreementList: PACKAGEAGREEMENT,
      showAgreement: false,
      isReadAgreement: false,
    }
  },
  onShow() {
	},
  onLoad() {
    this.paramsData = this.$store.state.appoint.select
    this.orderInfo = this.$Route.query.data
    let mealTypeList = []
    this.orderInfo.meal_type.map((meal, index) => {
      mealTypeList.push({
        key: meal,
        name: this.orderInfo.meal_type_alias[index]
      })
    })
    this.allMealTypeList = this.allMealTypeList.concat(mealTypeList)
    this.allOrderList = []
    this.orderInfo.all_date.map(date => {
      mealTypeList.map(meal => {
        let disabled = false
        if (this.orderInfo.toll_type === 'real_time') { // 实时收费
          if (new Date(date + ' 23:59:59') < new Date()) { //今天之前
            disabled = true
          }
          if (timeFormat(new Date(), 'yyyy-mm-dd') === date) { //为了计算当天餐段的disabled
            if (this.orderInfo.now_meal_type) { // now_meal_type为当前餐段
              let meal_keys_arr = Object.keys(this.mealKey);
              let nowIndex = meal_keys_arr.indexOf(this.orderInfo.now_meal_type)
              let index = meal_keys_arr.indexOf(meal.key)
              if (index < nowIndex) {
                disabled = true
              } else {
                disabled = false
              }
            } else {
              disabled = true
            }
          }
        }
        this.allOrderList.push({
          report_date: date,
          monthDate: timeFormat(new Date(date), 'mm月dd日 星期w'),
          meal_type: meal.key,
          meal_type_name: meal.name,
          report_fee: disabled ? 0 : Number(this.orderInfo[this.mealKey[meal.key] + '_fixed']),
          fuwu_fee: Number(this.orderInfo.fuwu_fee),
          disabled: disabled
        })
        if (!disabled) {
          this.totalFee = this.totalFee + Number(this.orderInfo[this.mealKey[meal.key] + '_fixed']) + Number(this.orderInfo.fuwu_fee)
        }
      })
    })
    this.canBuyList = this.allOrderList.filter(item => item.disabled === false)
    this.currentOrderList = this.allOrderList
  },
  methods: {
		divide,
    // 保存下已同意的协议
    changeAgreement(e) {
      console.log(e)
      this.readAgreement = e
      // Cache.set('READAGREEMENT', e)
    },
    changeMealType(item) {
      this.mealType = item.key
      if (item.key === 'all') {
        this.currentOrderList = this.allOrderList
      } else {
        this.currentOrderList = this.allOrderList.filter(order => order.meal_type === item.key)
      }
    },
    scrollBottom(e) {
      this.isReadAgreement = true
    },
    confirmPopup() {
      this.readAgreement = ['package']
    },
    onSubmitOrder() {
      // 加多个判断，有些情况是loading弹窗很慢的
      if (this.loading) {
        return
      }
      if (!this.canBuyList.length) return
      // if (this.agreementList.length && !this.readAgreement.length) {
      //   uni.$u.toast('请认真阅读订餐须知！')
      //   return
      // }
      this.$showLoading({
			  title: '正在为您创建订单....',
			  mask: true
			})
      this.loading = true
      let report_data = []
      this.allOrderList.map(item => {
        if (!item.disabled) {
          report_data.push({
            fuwu_fee: item.fuwu_fee,
            report_fee: item.report_fee,
            meal_type: item.meal_type,
            report_date: item.report_date,
          })
        }
      })
      getApiReportMealPackOrderCreate({
        person_no: this.paramsData.person.person_no,
        take_meal_type: this.paramsData.take_meal_type,
        payment_order_type: this.paramsData.payment_order_type,
        org_id: this.paramsData.org.org_id,
        company_id: Cache.get('userInfo').company_id,
        user_id: Cache.get('userInfo').user_id,
        report_meal_pack_settings_id: this.orderInfo.id,
        all_date_fee: this.totalFee,
        report_data: report_data
      })
        .then(res => {
          if (res.code == 0) {
            uni.hideLoading()
            this.$miRouter.replace({
              path: '/pages_bundle/payment/payment',
              query: {
                // #ifdef MP-ALIPAY
                data: this.$encodeQuery(res.data),
                // #endif
                // #ifndef MP-ALIPAY
                data: res.data,
                // #endif
              }
            })
          } else {
            uni.hideLoading()
            uni.$u.toast(res.msg)
          }
        })
        .catch(err => {
          uni.$u.toast(err.data.msg)
        })
        .finally(res => {
          this.loading = false
        })
    }
  }
}
</script>

<style lang="scss">
.meal-package-confirm {
  padding: 30rpx;
  .card-white{
    background-color: #fff;
    border-radius: 20rpx;
    padding: 30rpx;
  }
  .meal-type{
    display: flex;
    flex-wrap: wrap;
    .meal-type-item{
      background-color: #fff;
      padding: 10rpx 30rpx;
      margin-right: 20rpx;
      border-radius: 12rpx;
      margin-bottom: 15rpx;
    }
    .meal-type-item-active{
      background-color: $color-primary;
      color: #fff;
    }
  }
  .meal-list{
    margin-bottom: 150rpx;
  }
  .meal-item{
    display: flex;
    justify-content: space-between;
    line-height: 54rpx;
  }
  .footer {
    width: 100%;
    position: fixed;
    bottom: 0;
    left: 0;
    padding-bottom: env(safe-area-inset-bottom);

    &--warpper {
      height: 100rpx;
      padding-left: 38rpx;
    }

    .submit-btn {
      width: 280rpx;
      background-color: $color-primary;
      margin: 12rpx 20rpx;
      border-radius: 60rpx;
      font-size: 32rpx;
    }
    .un-submit-btn{
      background-color: #AAAAAA;
    }
  }
  .agreement-popup{
    padding: 30rpx;
    height: 700rpx;
    .popup-title{
      font-size: 32rpx;
      text-align: center;
      margin-bottom: 20rpx;
    }
    .info-title{
      font-weight: bold;
      font-size: 30rpx;
      margin-bottom: 15rpx;
    }
  }
}
</style>
