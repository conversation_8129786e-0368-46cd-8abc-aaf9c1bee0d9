<template>
  <view :style="theme.style" class="consumption-limit">
    <!-- 滚动容器 -->
    <!-- <scroll-view class="scroll-container" scroll-y="true" :style="{ height: scrollHeight + 'rpx' }"> -->
    <!-- 提示信息 -->
    <view class="tip-info">
      <view class="muted">请注意, 此功能只支持线下店内支付,且不兼容设备开启离线支付。</view>
      <view class="muted">如设置单独消费点限额,优先限制消费点,再限制全局。</view>
    </view>
    <!-- 全局限制 -->
    <view class="setting-section bg-white">
      <view class="section-title">
        <view class="flex row-between col-center">
          <view class="black nr">全局限制</view>
          <u-switch
            :activeColor="variables.colorPrimary"
            v-model="globalLimit.enabled"
            @change="onGlobalLimitChange"
            :size="34"
          ></u-switch>
        </view>
      </view>

      <!-- 每日限制金额 -->
      <view class="setting-item" v-if="globalLimit.enabled">
        <view class="flex row-between col-center">
          <view class="black nr">每日限制金额（元）</view>
          <view class="input-wrapper">
            {{ globalLimit.dailyAmount }}
            <u-number-box inputWidth="80px" @change="changeDailyAmount" @blur="blurDailyAmount" integer :min='0' :step="1" v-model="globalLimit.dailyAmount">
              <view slot="minus">
                <view class="minus">
                  <u-icon name="minus" color="inherit" size="20rpx"></u-icon>
                </view>
              </view>
              <view slot="plus" class="plus">
                <u-icon name="plus" color="inherit" size="20rpx"></u-icon>
              </view>
            </u-number-box>
          </view>
        </view>
      </view>
    </view>
    <view class="setting-section bg-white">
      <!-- 单独消费限制设置 -->
      <view class="setting-item">
        <view class="flex row-between col-center">
          <view class="black nr">单独消费限制设置</view>
          <u-switch
            :activeColor="variables.colorPrimary"
            v-model="individualLimit.enabled"
            @change="onIndividualLimitChange"
            :size="34"
          ></u-switch>
        </view>
      </view>
      <!-- 消费点设置列表 -->
      <view class="consumption-points" v-if="individualLimit.enabled">
        <view class="points-list">
          <view class="point-item" v-for="(point, index) in consumptionPoints" :key="index">
            <view class="point-header">
              <view class="flex row-between col-center">
                <view class="black nr">{{ point.name }}</view>
                <u-switch
                  :activeColor="variables.colorPrimary"
                  v-model="point.enabled"
                  @change="onPointLimitChange(index)"
                  :size="34"
                ></u-switch>
              </view>
            </view>

            <!-- 每日限制金额 -->
            <view class="point-amount" v-if="point.enabled">
              <view class="flex row-between col-center">
                <view class="black nr">每日限制金额（元）</view>
                <view class="input-wrapper">
                  <u-number-box inputWidth="80px" integer min="1" :step="1" v-model="point.dailyAmount">
                    <view slot="minus">
                      <view class="minus">
                        <u-icon name="minus" color="inherit" size="20rpx"></u-icon>
                      </view>
                    </view>
                    <view slot="plus" class="plus">
                      <u-icon name="plus" color="inherit" size="20rpx"></u-icon>
                    </view>
                  </u-number-box>
                </view>
              </view>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 底部占位，避免内容被固定按钮遮挡 -->
    <!-- <view class="bottom-placeholder"></view> -->
    <!-- </scroll-view> -->

    <!-- 保存按钮 -->
    <view class="footer bg-white">
      <view class="footer-wrapper flex flex-center">
        <u-button text="保存设置" shape="circle" :color="variables.bgLinearGradient1" @click="saveSettings" :loading="saving"></u-button>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      saving: false,
      scrollHeight: 0, // 滚动容器高度
      // 全局限制设置
      globalLimit: {
        enabled: true,
        dailyAmount: '10.00'
      },
      // 单独消费限制设置
      individualLimit: {
        enabled: true
      },
      // 消费点列表
      consumptionPoints: [
        {
          name: '消费点名称1',
          enabled: true,
          dailyAmount: '200.00'
        },
        {
          name: '消费点名称2',
          enabled: false,
          dailyAmount: '200.00'
        },
        {
          name: '消费点名称3',
          enabled: true,
          dailyAmount: '200.00'
        }
      ]
    }
  },

  methods: {
    // 全局限制开关变化
    onGlobalLimitChange(value) {
      this.globalLimit.enabled = value
      // if (!value) {
      // 关闭全局限制时，也关闭单独限制
      // this.individualLimit.enabled = false
      // }
    },

    // 单独消费限制开关变化
    onIndividualLimitChange(value) {
      this.individualLimit.enabled = value
    },

    // 消费点限制开关变化
    onPointLimitChange(index) {
      // 开关状态已经通过v-model自动更新
      console.log(`消费点${index + 1}限制状态:`, this.consumptionPoints[index].enabled)
    },
    // 加载当前设置
    async loadSettings() {
      try {
        // 这里添加获取当前设置的API调用
        // const response = await api.getConsumptionLimitSettings()
        // this.globalLimit = response.globalLimit
        // this.individualLimit = response.individualLimit
        // this.consumptionPoints = response.consumptionPoints
      } catch (error) {
        console.error('加载设置失败:', error)
      }
    },
    changeDailyAmount(e){
      this.globalLimit.dailyAmount = e.value
      console.log(e)
    },
    blurDailyAmount(value) {
      this.globalLimit.dailyAmount = value.value
      console.log(this.globalLimit.dailyAmount)
      
      if (Number(value.value) <=1 || value.value === '') {
      console.log('blurDailyAmount', value)
      this.globalLimit.dailyAmount = 1
      }
    },
    // 保存设置
    async saveSettings() {
      this.saving = true
      try {
        // 这里添加保存设置的API调用
        const settings = {
          globalLimit: this.globalLimit,
          individualLimit: this.individualLimit,
          consumptionPoints: this.consumptionPoints
        }

        console.log('保存设置:', settings)

        // 模拟API调用
        await new Promise(resolve => setTimeout(resolve, 1000))

        uni.showToast({
          title: '保存成功',
          icon: 'success'
        })

        // 可以选择保存后返回上一页
        // setTimeout(() => {
        //   uni.navigateBack()
        // }, 1500)
      } catch (error) {
        console.error('保存失败:', error)
        uni.showToast({
          title: '保存失败',
          icon: 'error'
        })
      } finally {
        this.saving = false
      }
    }
  },

  onLoad() {
    // // 页面加载时可以从API获取当前设置
    // this.loadSettings()
  },

  onReady() {}
}
</script>

<style lang="scss" scoped>
.consumption-limit {
  height: calc(100vh - 180rpx);
  overflow: auto;
  padding: 40rpx;
  background-color: $background-color;
  .bottom-placeholder {
    height: 40rpx; // 底部留白，避免内容被按钮遮挡
  }
  .tip-info {
    margin-bottom: 20rpx;

    text {
      font-size: 24rpx;
      line-height: 1.5;
    }
  }
  .setting-section {
    margin-bottom: 20rpx;
    border-radius: 20rpx;
    padding: 0 40rpx;

    .section-title {
      padding: 40rpx 0;
      border-bottom: 1rpx solid $border-color-base;
    }
    .setting-item {
      padding: 20rpx 0;
      &:not(:last-child) {
        border-bottom: 1rpx solid $border-color-base;
      }
    }
    .minus,
    .plus {
      color: $color-primary;
      width: 36rpx;
      height: 36rpx;
      display: flex;
      justify-content: center;
      align-items: center;
      border: 1px solid $color-primary;
      border-radius: 4rpx;
    }

    .plus {
      margin-left: 20rpx;
      background-color: $color-primary;
      color: #fff;
      border-color: $color-primary;
    }
    .minus {
      margin-right: 20rpx;
    }
  }
  .consumption-points {
    border-radius: 20rpx;
    margin-bottom: 20rpx;
    .points-list {
      .point-item {
        &:not(:last-child) {
          border-bottom: 1rpx solid $border-color-base;
        }
        .point-header {
          padding: 40rpx 0;
        }
        .point-amount {
          padding-bottom: 40rpx;
        }
      }
    }
  }

  .footer {
    width: 100%;
    position: fixed;
    bottom: 0;
    left: 0;
    padding: 0 40rpx;
    border-top: 1px solid #eaecee;
    padding-bottom: env(safe-area-inset-bottom);

    .footer-wrapper {
      padding-top: 20rpx;
      padding-bottom: 20rpx;
    }
  }
}
</style>
