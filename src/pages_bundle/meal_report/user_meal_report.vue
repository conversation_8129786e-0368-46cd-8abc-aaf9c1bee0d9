<template>
  <view class="user-meal-report" :style="theme.style + `height: ${current == 1 ? '94vh' : '74vh'};`">
    <view class="tabs bg-white">
      <u-tabs
        :list="tabs"
        :line-color="variables.colorPrimary"
        :active-style="{ fontWeight: 500, color: '#101010' }"
        @change="tabsChange"
      ></u-tabs>
    </view>
    <view class="tabs-content flex-1">
      <view style="height: 100%" v-for="(item, index) in tabs" :key="index" v-show="current == index">
        <appointment-lists ref="appointmentListsRef" :type="item.type" order-type="report_meal" :bottom="current == 0 ? 120 : 0" mode="report"></appointment-lists>
      </view>
    </view>
    <view class="meal-report-btn flex row-around" v-if="current == 0">
      <view class="flex-1" @click="gotoPath('meal_reprot')" v-if="reportMeal">
        <u-button text="去报餐" shape="circle" :color="variables.colorPrimary"></u-button>
      </view>
      <view class="flex-1" @click="gotoPath('meal_pack')" v-if="mealPack">
        <u-button text="餐包预订" shape="circle" :color="variables.colorPrimaryLight10"></u-button>
      </view>
      <!-- <view class="flex-1" @click="gotoApply" v-if="current == 1 && cancelReviews">
        <u-button text="批量取消" shape="circle" plain color="#11E6C5"></u-button>
      </view> -->
    </view>
    <!-- 弹窗 -->
    <popup></popup>
    <floating-popup :floatingPopupShow="floatingPopupShow"></floating-popup>
  </view>
</template>

<script>
import appointmentLists from '../components/appointment/lists.vue'
import Cache from '@/utils/cache'
import { getApiBookingUserGetCardUserList, getApiBookingUserGetCanteenList, getApiTakeMealTypeList, getApiUserReportMealSettings } from '@/api/reservation'
import { mapMutations } from 'vuex'
import FloatingPopup from '@/components/floating-popup/floatingPopup.vue'
export default {
  components: {
    appointmentLists,
    FloatingPopup
  },
  data() {
    return {
      current: 0,
      tabs: [
        {
          name: '当餐',
          type: 'now'
        },
        {
          name: '汇总',
          type: 'all'
        }
      ],
      checked: [],
			cancelReviews:true,
			reportMeal: false,
			mealPack: false,
      userinfo: {},
      floatingPopupShow: false,
      gotoType: ''
    }
  },
  onShow() {
		// 要做刷新哦，防止页面回退数据不是最新的
    let appointmentListsRef = this.$refs.appointmentListsRef
		if (appointmentListsRef) {
			if (Array.isArray(appointmentListsRef)) {
				appointmentListsRef.forEach(v => {
					v.initMescroll()
				})
			}
		}
    this.floatingPopupShow = !this.floatingPopupShow
	},
  mounted() {
    this.userinfo = Cache.get('userInfo')
    this.$eventbus.$on('changeCalendarPicker', val => {
      this.showAppointBtn = !val
    })
		this.getUserReportMealSettings()
  },
  methods: {
    // toJSON() {
    //   return this
    // },
    ...mapMutations(['SET_SELECT']),
    tabsChange({ index }) {
      this.current = index
    },
    // 获取食堂
    getBookingUserGetCardUserList() {
      this.$showLoading({
        title: '获取中....',
        mask: true
      })
      getApiBookingUserGetCardUserList({
        company_id: this.userinfo.company_id
      })
        .then(res => {
          uni.hideLoading()
          if (res.code == 0) {
            if (res.data.length && res.data.length <= 1) {
              this.getBookingUserGetCanteenList(res.data[0])
            } else {
              if (this.gotoType === 'meal_reprot') {
                this.$miRouter.push({
                  path: '/pages_bundle/select/select_diner?type=report_meal'
                })
              } else if (this.gotoType === 'meal_pack') {
                this.$miRouter.push({
                  path: '/pages_bundle/select/select_diner',
                  query: {
                    type: 'report_meal',
                    meal_pack: 1
                  }
                })
              }
            }
            // if (this.$store.state.appoint.select.person.person_no) {
            // 	this.getBookingUserGetCanteenList(this.$store.state.appoint.select.person)
            // }
          } else {
            // console.log(res)
            uni.$u.toast(res.msg)
          }
        })
        .catch(error => {
          uni.hideLoading()
          // console.log(error)
          uni.$u.toast(error.message)
        })
    },
    // 获取食堂组织
    getBookingUserGetCanteenList(personData) {
      getApiBookingUserGetCanteenList({
        groups: personData.groups,
        company_id: this.userinfo.company_id,
        user_id: this.userinfo.user_id,
        h5_type: 'report_meal'
      })
        .then(res => {
          if (res.code == 0) {
            if (res.data.length && res.data.length <= 1) {
              this.getTakeMealTypeList(personData, res.data[0])
            } else {
              if (this.gotoType === 'meal_reprot') {
                this.$miRouter.push({
                  path: '/pages_bundle/select/select_diner?type=report_meal'
                })
              } else if (this.gotoType === 'meal_pack') {
                this.$miRouter.push({
                  path: '/pages_bundle/select/select_diner',
                  query: {
                    type: 'report_meal',
                    meal_pack: 1
                  }
                })
              }
            }
            // if (this.$store.state.appoint.select.org.org_id) {
            // 	this.getTakeMealTypeList(this.$store.state.appoint.select.person,this.$store.state.appoint.select.org.org_id)
            // }
          } else {
            // console.log(res)
            uni.$u.toast(res.msg)
          }
        })
        .catch(error => {
          // console.log(error)
          uni.$u.toast(error.message)
        })
    },
    // 获取堂食方式
    getTakeMealTypeList(personData, org) {
      getApiTakeMealTypeList({
        groups: personData.groups,
        organization_id: org.org_id,
        h5_type: 'report_meal'
      })
        .then(res => {
          if (res.code == 0) {
            if (res.data.length && res.data.length <= 1) {
              this.SET_SELECT({
                key: 'person',
                data: personData
              })
              this.SET_SELECT({
                key: 'org',
                data: org
              })
              this.SET_SELECT({
                key: 'take_meal_type',
                data: res.data[0].take_meal
              })
              if (this.gotoType === 'meal_reprot') {
                this.$miRouter.push({
                  path: '/pages_bundle/meal_report/meal_report'
                })
              } else if (this.gotoType === 'meal_pack') {
                this.$miRouter.replace({
                  path: '/pages_bundle/meal_report/meal_package'
                })
              }
            } else {
              if (this.gotoType === 'meal_reprot') {
                this.$miRouter.push({
                  path: '/pages_bundle/select/select_diner?type=report_meal'
                })
              } else if (this.gotoType === 'meal_pack') {
                this.$miRouter.push({
                  path: '/pages_bundle/select/select_diner',
                  query: {
                    type: 'report_meal',
                    meal_pack: 1
                  }
                })
              }
            }
          } else {
            uni.$u.toast(res.msg)
          }
        })
        .catch(error => {
          uni.$u.toast(error.message)
        })
    },
		// 隐藏批量
		getUserReportMealSettings() {
			getApiUserReportMealSettings({
				company_id: this.userinfo.company_id,
				person_no:this.userinfo.person_no 
			})
				.then(res => {
					if (res.code == 0) {
						this.cancelReviews = res.data.cancel_reviews
						this.reportMeal = res.data.report_meal
						this.mealPack = res.data.meal_pack
					} else {
						uni.$u.toast(res.msg)
					}
				})
				.catch(error => {
					uni.$u.toast(error.message)
				})
		},
    gotoPath(type) {
      this.gotoType = type
      this.getBookingUserGetCardUserList()
    },
    gotoApply() {
      this.$miRouter.push({
      	path: '/pages_order/review/apply/select_meal',
				query: {
					type: 'report_meal'
				}
      })
    },
    gotoMealPackage() {
      this.$miRouter.push({
      	path: '/pages_bundle/select/select_diner',
				query: {
					type: 'report_meal',
          meal_pack: 1
				}
      })
      // this.$miRouter.push({
      // 	path: '/pages_bundle/meal_report/meal_package'
      // })
    }
  }
}
</script>

<style lang="scss">
page {
  padding: 0;
}
.user-meal-report {
  height: 100vh;
  display: flex;
  flex-direction: column;
  .tabs-content {
    min-height: 0;
  }

  .meal-report-btn {
    position: fixed;
    left: 0;
    bottom: 40rpx;
    width: 100%;
    padding: 0 40rpx;
    height: 80rpx;
    .flex-1{
      flex: 1;
      padding: 0 10rpx;
    }
  }
}
</style>
